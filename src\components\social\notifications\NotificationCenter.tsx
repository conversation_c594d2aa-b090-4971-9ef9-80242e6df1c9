'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Heart, MessageCircle, UserPlus, AtSign, Eye } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import type { SocialNotificationType, NotificationGroup } from '@/types/social';

// =====================================================
// NOTIFICATION CENTER - COMPONENTE PRINCIPAL
// =====================================================

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export function NotificationCenter({ isOpen, onClose, className = '' }: NotificationCenterProps) {
  const { groupedNotifications, unreadCount, loading, error, markAsRead, markAllAsRead } = useNotifications();
  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all');

  if (!isOpen) return null;

  const filteredNotifications = activeTab === 'unread' 
    ? groupedNotifications.filter(n => !n.is_read)
    : groupedNotifications;

  return (
    <div className={`fixed inset-0 z-50 ${className}`}>
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black/20 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Panel */}
      <div className="absolute top-16 right-4 w-96 max-w-[calc(100vw-2rem)] bg-white dark:bg-gray-900 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 max-h-[80vh] flex flex-col">
        
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Notificações
            </h2>
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('all')}
              className={`flex-1 px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'all'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Todas
            </button>
            <button
              onClick={() => setActiveTab('unread')}
              className={`flex-1 px-3 py-1.5 text-sm font-medium rounded-md transition-colors relative ${
                activeTab === 'unread'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Não lidas
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>
          </div>

          {/* Actions */}
          {unreadCount > 0 && (
            <div className="mt-3 flex justify-end">
              <button
                onClick={markAllAsRead}
                className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium flex items-center space-x-1"
              >
                <CheckCheck className="w-4 h-4" />
                <span>Marcar todas como lidas</span>
              </button>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="p-8 text-center">
              <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
              <p className="text-sm text-gray-500">Carregando notificações...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="p-8 text-center">
              <Bell className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {activeTab === 'unread' ? 'Nenhuma notificação não lida' : 'Nenhuma notificação'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100 dark:divide-gray-800">
              {filteredNotifications.map((notification, index) => (
                <NotificationItem
                  key={`${notification.type}_${notification.post_id}_${index}`}
                  notification={notification}
                  onMarkAsRead={markAsRead}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// =====================================================
// NOTIFICATION ITEM - ITEM INDIVIDUAL
// =====================================================

interface NotificationItemProps {
  notification: NotificationGroup;
  onMarkAsRead: (notificationId: string) => void;
}

function NotificationItem({ notification, onMarkAsRead }: NotificationItemProps) {
  const getNotificationIcon = (type: SocialNotificationType) => {
    switch (type) {
      case 'like': return <Heart className="w-5 h-5 text-red-500" />;
      case 'comment': return <MessageCircle className="w-5 h-5 text-blue-500" />;
      case 'follow': return <UserPlus className="w-5 h-5 text-green-500" />;
      case 'mention': return <AtSign className="w-5 h-5 text-purple-500" />;
      case 'reply': return <MessageCircle className="w-5 h-5 text-blue-500" />;
      case 'story_view': return <Eye className="w-5 h-5 text-gray-500" />;
      default: return <Bell className="w-5 h-5 text-gray-500" />;
    }
  };

  const getNotificationText = (notification: NotificationGroup) => {
    const { type, count, latest_actor, other_actors } = notification;
    const actorName = latest_actor.display_name || latest_actor.username;
    
    if (count === 1) {
      switch (type) {
        case 'like': return `${actorName} curtiu seu post`;
        case 'comment': return `${actorName} comentou em seu post`;
        case 'follow': return `${actorName} começou a seguir você`;
        case 'mention': return `${actorName} mencionou você`;
        case 'reply': return `${actorName} respondeu seu comentário`;
        case 'story_view': return `${actorName} viu seu story`;
        default: return `${actorName} interagiu com você`;
      }
    } else {
      const othersCount = other_actors.length;
      const othersText = othersCount > 0 ? ` e mais ${othersCount} ${othersCount === 1 ? 'pessoa' : 'pessoas'}` : '';
      
      switch (type) {
        case 'like': return `${actorName}${othersText} curtiram seu post`;
        case 'comment': return `${actorName}${othersText} comentaram em seu post`;
        case 'follow': return `${actorName}${othersText} começaram a seguir você`;
        case 'mention': return `${actorName}${othersText} mencionaram você`;
        case 'reply': return `${actorName}${othersText} responderam seu comentário`;
        case 'story_view': return `${actorName}${othersText} viram seu story`;
        default: return `${actorName}${othersText} interagiram com você`;
      }
    }
  };

  const timeAgo = formatDistanceToNow(new Date(notification.created_at), {
    addSuffix: true,
    locale: ptBR
  });

  return (
    <div 
      className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${
        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
      onClick={() => {
        // TODO: Navegar para o post/perfil relevante
        console.log('Clicked notification:', notification);
      }}
    >
      <div className="flex items-start space-x-3">
        {/* Avatar */}
        <div className="relative">
          <img
            src={notification.latest_actor.avatar_url || '/default-avatar.png'}
            alt={notification.latest_actor.username}
            className="w-10 h-10 rounded-full object-cover"
          />
          <div className="absolute -bottom-1 -right-1 bg-white dark:bg-gray-900 rounded-full p-1">
            {getNotificationIcon(notification.type)}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <p className="text-sm text-gray-900 dark:text-white">
            {getNotificationText(notification)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {timeAgo}
          </p>
        </div>

        {/* Unread indicator */}
        {!notification.is_read && (
          <div className="flex-shrink-0">
            <button
              onClick={(e) => {
                e.stopPropagation();
                // TODO: Implementar markAsRead para grupos
                console.log('Mark as read:', notification);
              }}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full transition-colors"
              title="Marcar como lida"
            >
              <Check className="w-4 h-4 text-gray-400" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

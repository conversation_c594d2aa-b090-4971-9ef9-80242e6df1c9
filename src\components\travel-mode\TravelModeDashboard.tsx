"use client";

import React, { useState, useEffect } from 'react';
import { 
  MapPin, 
  Clock, 
  CloudSun, 
  Navigation, 
  Activity, 
  DollarSign, 
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Map,
  BookOpen,
  CreditCard,
  Bot,
  Bell,
  BarChart3,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  getTravelModeDashboardAction
} from '@/actions/travel-mode/travel-mode-actions';
import { useTravelModeRealtime } from '@/hooks/useTravelModeRealtime';
import TravelModeMap from './TravelModeMap';
import TravelModeNotifications from './TravelModeNotifications';
import TravelModeExpenses from './TravelModeExpenses';
import TravelModeDiary from './TravelModeDiary';
import TravelModeAssistant from './TravelModeAssistant';
import type { 
  TravelModeDashboard as TravelModeDashboardType
} from '@/types/travel-mode';

interface TravelModeDashboardProps {
  itineraryId: string;
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

function MetricCard({ title, value, change, changeType = 'neutral', icon, className, onClick }: MetricCardProps) {
  const Component = onClick ? 'button' : 'div';
  
  return (
    <Component 
      onClick={onClick}
      className={cn(
        "relative h-full rounded-2xl p-6 text-left",
        "bg-white dark:bg-zinc-900/50",
        "border border-zinc-200 dark:border-zinc-800",
        "hover:border-zinc-300 dark:hover:border-zinc-700",
        "transition-all duration-300",
        onClick && "hover:scale-[1.02] cursor-pointer active:scale-[0.98]",
        className
      )}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="p-2 rounded-full bg-zinc-100 dark:bg-zinc-800/50">
          {icon}
        </div>
        {change !== undefined && (
          <div className={cn(
            "flex items-center gap-1 text-sm font-medium",
            changeType === 'positive' && "text-emerald-600 dark:text-emerald-400",
            changeType === 'negative' && "text-red-600 dark:text-red-400",
            changeType === 'neutral' && "text-zinc-500 dark:text-zinc-400"
          )}>
            {changeType === 'positive' && <TrendingUp className="w-4 h-4" />}
            {changeType === 'negative' && <TrendingDown className="w-4 h-4" />}
            {change > 0 ? '+' : ''}{change}%
          </div>
        )}
      </div>
      <div className="space-y-1">
        <h3 className="text-2xl font-bold text-zinc-900 dark:text-white">
          {value}
        </h3>
        <p className="text-sm text-zinc-500 dark:text-zinc-400">
          {title}
        </p>
      </div>
      {onClick && (
        <div className="absolute bottom-4 right-4 opacity-50 group-hover:opacity-100 transition-opacity">
          <ArrowRight className="w-4 h-4" />
        </div>
      )}
    </Component>
  );
}

interface ActivityCardProps {
  activity?: {
    id: string;
    title: string;
    description?: string;
    location_name?: string;
    start_time?: string;
    end_time?: string;
    day_number: number;
  } | undefined;
  type: 'current' | 'next';
  className?: string;
}

function ActivityCard({ activity, type, className }: ActivityCardProps) {
  if (!activity) {
    return (
      <div className={cn(
        "rounded-2xl p-6 border-2 border-dashed",
        "border-zinc-200 dark:border-zinc-800",
        "flex items-center justify-center",
        className
      )}>
        <div className="text-center text-zinc-500 dark:text-zinc-400">
          <Clock className="w-8 h-8 mx-auto mb-2" />
          <p className="text-sm">
            {type === 'current' ? 'Nenhuma atividade em andamento' : 'Próxima atividade não definida'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "relative h-full rounded-2xl p-6",
      "bg-gradient-to-br from-blue-50 to-indigo-50",
      "dark:from-blue-950/30 dark:to-indigo-950/30",
      "border border-blue-200 dark:border-blue-800",
      "hover:shadow-lg transition-all duration-300",
      type === 'current' && "ring-2 ring-blue-400 dark:ring-blue-600",
      className
    )}>
      <div className="flex items-start justify-between mb-4">
        <div className={cn(
          "px-3 py-1 rounded-full text-xs font-medium",
          type === 'current' 
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
        )}>
          {type === 'current' ? 'Em Andamento' : 'Próxima'}
        </div>
        {activity.start_time && (
          <div className="text-sm text-zinc-500 dark:text-zinc-400">
            {new Date(activity.start_time).toLocaleTimeString('pt-BR', { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </div>
        )}
      </div>
      
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-zinc-900 dark:text-white">
          {activity.title}
        </h3>
        
        {activity.description && (
          <p className="text-sm text-zinc-600 dark:text-zinc-300 line-clamp-2">
            {activity.description}
          </p>
        )}
        
        {activity.location_name && (
          <div className="flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400">
            <MapPin className="w-4 h-4" />
            <span>{activity.location_name}</span>
          </div>
        )}
        
        <button className={cn(
          "inline-flex items-center gap-2 px-4 py-2 rounded-lg",
          "bg-white dark:bg-zinc-800 text-zinc-900 dark:text-white",
          "border border-zinc-200 dark:border-zinc-700",
          "hover:bg-zinc-50 dark:hover:bg-zinc-700",
          "transition-colors text-sm font-medium"
        )}>
          <Navigation className="w-4 h-4" />
          {type === 'current' ? 'Direções' : 'Ver Detalhes'}
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

interface QuickStatsProps {
  dashboard: TravelModeDashboardType;
  onTabChange: (tab: string) => void;
}

function QuickStats({ dashboard, onTabChange }: QuickStatsProps) {
  const stats = [
    {
      title: "Gastos Hoje",
      value: dashboard.expense_summary?.today_spent ? `R$ ${dashboard.expense_summary.today_spent.toFixed(2)}` : "R$ 0,00",
      change: dashboard.expense_summary?.trend_percentage || 0,
      changeType: dashboard.expense_summary?.trend === 'up' ? 'negative' as const : 'positive' as const,
      icon: <DollarSign className="w-5 h-5 text-emerald-600" />,
      onClick: () => onTabChange('expenses')
    },
    {
      title: "Atividades Visitadas",
      value: dashboard.day_progress?.completed_activities || 0,
      change: 0,
      changeType: 'neutral' as const,
      icon: <CheckCircle className="w-5 h-5 text-blue-600" />,
      onClick: () => onTabChange('map')
    },
    {
      title: "Entradas no Diário",
      value: dashboard.diary_entries?.length || 0,
      change: 0,
      changeType: 'neutral' as const,
      icon: <BookOpen className="w-5 h-5 text-purple-600" />,
      onClick: () => onTabChange('diary')
    },
    {
      title: "Notificações",
      value: dashboard.notifications?.length || 0,
      change: 0,
      changeType: 'neutral' as const,
      icon: <Bell className="w-5 h-5 text-orange-600" />,
      onClick: () => onTabChange('notifications')
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <MetricCard
          key={index}
          title={stat.title}
          value={stat.value}
          change={stat.change}
          changeType={stat.changeType}
          icon={stat.icon}
          onClick={stat.onClick}
          className="group"
        />
      ))}
    </div>
  );
}

export default function TravelModeDashboard({ itineraryId, className }: TravelModeDashboardProps) {
  const [dashboard, setDashboard] = useState<TravelModeDashboardType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [sseEnabled, setSseEnabled] = useState(false);

  // Hook real-time moderno substituindo polling (só ativa após carregamento inicial)
  const {
    isConnected,
    reconnect,
    updateData
  } = useTravelModeRealtime(itineraryId, {
    enabled: sseEnabled,
    reconnectOnError: true,
    maxReconnectAttempts: 5
  });

  // Atualiza dados iniciais no hook de tempo real
  useEffect(() => {
    if (dashboard && updateData) {
      const locationData = dashboard.current_location ? {
        latitude: dashboard.current_location.lat,
        longitude: dashboard.current_location.lng,
        ...(dashboard.current_location.address && { address: dashboard.current_location.address })
      } : undefined;
      
      updateData({
        currentLocation: locationData
      });
    }
  }, [dashboard, updateData]);

  // Carregamento inicial via server action com fallback para API
  const loadInitialDashboard = async () => {
    try {
      setIsLoading(true);

      // Tentar server action primeiro
      try {
        const result = await getTravelModeDashboardAction(itineraryId);

        if (result.success) {
          setDashboard(result.data);
          setError(null);
          // Ativa SSE apenas após carregamento bem-sucedido e documento pronto
          if (document.readyState === 'complete') {
            setSseEnabled(true);
          } else {
            // Aguarda documento estar completamente carregado
            const handleLoad = () => {
              setSseEnabled(true);
              window.removeEventListener('load', handleLoad);
            };
            window.addEventListener('load', handleLoad);
          }
          return;
        } else {
          console.warn('Server action failed, trying API fallback:', result.error);
        }
      } catch (actionError) {
        console.warn('Server action error, trying API fallback:', actionError);
      }

      // Fallback para API
      const response = await fetch(`/api/travel-mode/${itineraryId}/dashboard`);
      const apiResult = await response.json();

      if (apiResult.success) {
        setDashboard(apiResult.data);
        setError(null);
        // Ativa SSE apenas após carregamento bem-sucedido e documento pronto
        if (document.readyState === 'complete') {
          // Delay adicional para garantir que tudo está estável
          setTimeout(() => setSseEnabled(true), 1500);
        } else {
          // Aguarda documento estar completamente carregado
          const handleLoad = () => {
            // Delay adicional após load para garantir estabilidade
            setTimeout(() => setSseEnabled(true), 2000);
            window.removeEventListener('load', handleLoad);
          };
          window.addEventListener('load', handleLoad);
        }
      } else {
        setError(apiResult.error || 'Erro ao carregar dashboard');
      }
    } catch (err) {
      console.error('Dashboard loading error:', err);
      setError('Erro ao carregar dashboard');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInitialDashboard();
  }, [itineraryId]);

  const handleRefresh = () => {
    loadInitialDashboard();
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  if (isLoading) {
    return (
      <div className={cn(
        "flex items-center justify-center min-h-[400px]",
        className
      )}>
        <div className="flex items-center gap-3 text-zinc-500">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>Carregando dashboard...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn(
        "flex items-center justify-center min-h-[400px]",
        className
      )}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-zinc-900 dark:text-white mb-2">
            Erro ao carregar dashboard
          </h3>
          <p className="text-zinc-500 dark:text-zinc-400 mb-4">
            {error}
          </p>
          <button
            onClick={handleRefresh}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  if (!dashboard) {
    return null;
  }

  return (
    <div className={cn("w-full space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">
            Modo Viagem
          </h1>
          <p className="text-zinc-500 dark:text-zinc-400 mt-1">
            Dashboard em tempo real da sua viagem
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* Status de conexão real-time */}
          <div className={cn(
            "flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium",
            isConnected 
              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
          )}>
            <div className={cn(
              "w-2 h-2 rounded-full",
              isConnected ? "bg-green-500" : "bg-red-500"
            )} />
            {isConnected ? 'Tempo Real' : 'Desconectado'}
          </div>
          
          <button
            onClick={isConnected ? reconnect : loadInitialDashboard}
            className={cn(
              "inline-flex items-center gap-2 px-4 py-2 rounded-lg",
              "bg-zinc-100 dark:bg-zinc-800 text-zinc-900 dark:text-white",
              "hover:bg-zinc-200 dark:hover:bg-zinc-700",
              "transition-colors border border-zinc-200 dark:border-zinc-700"
            )}
          >
            <RefreshCw className="w-4 h-4" />
            {isConnected ? 'Reconectar' : 'Atualizar'}
          </button>
        </div>
      </div>

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6 lg:grid-cols-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            <span className="hidden sm:inline">Visão Geral</span>
          </TabsTrigger>
          <TabsTrigger value="map" className="flex items-center gap-2">
            <Map className="w-4 h-4" />
            <span className="hidden sm:inline">Mapa</span>
          </TabsTrigger>
          <TabsTrigger value="expenses" className="flex items-center gap-2">
            <CreditCard className="w-4 h-4" />
            <span className="hidden sm:inline">Gastos</span>
          </TabsTrigger>
          <TabsTrigger value="diary" className="flex items-center gap-2">
            <BookOpen className="w-4 h-4" />
            <span className="hidden sm:inline">Diário</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="w-4 h-4" />
            <span className="hidden sm:inline">Alertas</span>
          </TabsTrigger>
          <TabsTrigger value="assistant" className="flex items-center gap-2">
            <Bot className="w-4 h-4" />
            <span className="hidden sm:inline">Assistente</span>
          </TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <QuickStats dashboard={dashboard} onTabChange={handleTabChange} />
            
            {/* Current & Next Activities */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-zinc-900 dark:text-white flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Atividade Atual
                </h2>
                                 <ActivityCard 
                   activity={dashboard.current_activity} 
                   type="current" 
                 />
               </div>
               
               <div className="space-y-4">
                 <h2 className="text-xl font-semibold text-zinc-900 dark:text-white flex items-center gap-2">
                   <Calendar className="w-5 h-5" />
                   Próxima Atividade
                 </h2>
                 <ActivityCard 
                   activity={dashboard.next_activity} 
                   type="next" 
                 />
              </div>
            </div>

            {/* Weather Summary */}
            {dashboard.weather && (
              <div className="rounded-2xl p-6 bg-gradient-to-r from-sky-50 to-blue-50 dark:from-sky-950/30 dark:to-blue-950/30 border border-sky-200 dark:border-sky-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-full bg-sky-100 dark:bg-sky-900/50">
                      <CloudSun className="w-6 h-6 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-zinc-900 dark:text-white">
                        {dashboard.weather.description}
                      </h3>
                                             <p className="text-sky-600 dark:text-sky-400">
                         {dashboard.weather.temperature}°C • {dashboard.weather.description}
                       </p>
                    </div>
                  </div>
                  <div className="text-right text-sm text-zinc-500 dark:text-zinc-400">
                    <p>Umidade: {dashboard.weather.humidity}%</p>
                    <p>Vento: {dashboard.weather.windSpeed} km/h</p>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="map">
            <TravelModeMap
              {...(dashboard.current_location && { currentLocation: dashboard.current_location })}
              activities={[
                ...(dashboard.current_activity ? [{
                  id: dashboard.current_activity.id,
                  title: dashboard.current_activity.title,
                  location: {
                    lat: -23.5505, // Mock data - replace with real location
                    lng: -46.6333,
                    address: dashboard.current_activity.location_name || 'Localização atual'
                  },
                  status: 'current' as const
                }] : []),
                ...(dashboard.next_activity ? [{
                  id: dashboard.next_activity.id,
                  title: dashboard.next_activity.title,
                  location: {
                    lat: -23.5515, // Mock data - replace with real location  
                    lng: -46.6343,
                    address: dashboard.next_activity.location_name || 'Próximo destino'
                  },
                  status: 'upcoming' as const
                }] : [])
              ]}
              alternatives={dashboard.alternative_activities}
            />
          </TabsContent>

          <TabsContent value="expenses">
            <TravelModeExpenses
              itineraryId={itineraryId}
              expenses={[]}
              categories={dashboard.expense_categories || []}
              splits={dashboard.expense_splits || []}
              stats={dashboard.expense_summary}
              onRefresh={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="diary">
            <TravelModeDiary
              entries={dashboard.diary_entries || []}
              currentLocation={dashboard.current_location}
              currentWeather={dashboard.weather?.condition}
              onRefresh={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="notifications">
            <TravelModeNotifications
              itineraryId={itineraryId}
              notifications={dashboard.notifications}
              onNotificationUpdate={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="assistant">
            <TravelModeAssistant
              dashboard={dashboard}
              currentLocation={dashboard.current_location}
              weather={dashboard.weather}
              onRefresh={handleRefresh}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
} 
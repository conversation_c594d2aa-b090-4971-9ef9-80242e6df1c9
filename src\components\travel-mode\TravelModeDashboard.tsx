"use client";

import React, { useState, useEffect } from 'react';
import {
  MapPin,
  Clock,
  CloudSun,
  Navigation,
  Activity,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Map,
  BookOpen,
  CreditCard,
  Bot,
  Bell,
  BarChart3,
  Calendar,
  Eye,
  Play,
  SkipForward
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  getTravelModeDashboardAction,
  markActivityCompletedAction,
  startActivityAction,
  skipActivityAction
} from '@/actions/travel-mode/travel-mode-actions';
import { useTravelModeRealtime } from '@/hooks/useTravelModeRealtime';
import TravelModeMap from './TravelModeMap';
import TravelModeNotifications from './TravelModeNotifications';
import TravelModeExpenses from './TravelModeExpenses';
import TravelModeDiary from './TravelModeDiary';
import TravelModeAssistant from './TravelModeAssistant';
import type {
  TravelModeDashboard as TravelModeDashboardType
} from '@/types/travel-mode';

/**
 * Formata uma string de tempo (HH:MM:SS) para exibição (HH:MM)
 * Trata valores nulos/undefined e formatos inválidos
 */
function formatTimeFromString(timeString: string | null | undefined): string {
  if (!timeString) return '';

  try {
    // timeString vem como "09:30:00" do PostgreSQL
    const timeParts = timeString.split(':');
    if (timeParts.length >= 2) {
      const hours = timeParts[0].padStart(2, '0');
      const minutes = timeParts[1].padStart(2, '0');
      return `${hours}:${minutes}`;
    }
    return timeString;
  } catch {
    return '';
  }
}

interface TravelModeDashboardProps {
  itineraryId: string;
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

function MetricCard({ title, value, change, changeType = 'neutral', icon, className, onClick }: MetricCardProps) {
  const Component = onClick ? 'button' : 'div';
  
  return (
    <Component 
      onClick={onClick}
      className={cn(
        "relative h-full rounded-2xl p-6 text-left",
        "bg-white dark:bg-zinc-900/50",
        "border border-zinc-200 dark:border-zinc-800",
        "hover:border-zinc-300 dark:hover:border-zinc-700",
        "transition-all duration-300",
        onClick && "hover:scale-[1.02] cursor-pointer active:scale-[0.98]",
        className
      )}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="p-2 rounded-full bg-zinc-100 dark:bg-zinc-800/50">
          {icon}
        </div>
        {change !== undefined && (
          <div className={cn(
            "flex items-center gap-1 text-sm font-medium",
            changeType === 'positive' && "text-emerald-600 dark:text-emerald-400",
            changeType === 'negative' && "text-red-600 dark:text-red-400",
            changeType === 'neutral' && "text-zinc-500 dark:text-zinc-400"
          )}>
            {changeType === 'positive' && <TrendingUp className="w-4 h-4" />}
            {changeType === 'negative' && <TrendingDown className="w-4 h-4" />}
            {change > 0 ? '+' : ''}{change}%
          </div>
        )}
      </div>
      <div className="space-y-1">
        <h3 className="text-2xl font-bold text-zinc-900 dark:text-white">
          {value}
        </h3>
        <p className="text-sm text-zinc-500 dark:text-zinc-400">
          {title}
        </p>
      </div>
      {onClick && (
        <div className="absolute bottom-4 right-4 opacity-50 group-hover:opacity-100 transition-opacity">
          <ArrowRight className="w-4 h-4" />
        </div>
      )}
    </Component>
  );
}

interface ActivityCardProps {
  activity?: {
    id: string;
    title: string;
    description?: string;
    location_name?: string;
    start_time?: string;
    end_time?: string;
    day_number: number;
    status?: 'pending' | 'in_progress' | 'completed' | 'skipped';
    started_at?: string;
    completed_at?: string;
    notes?: string;
  } | undefined;
  type: 'current' | 'next';
  className?: string;
  onMarkCompleted?: (activityId: string, notes?: string) => Promise<void>;
  onStartActivity?: (activityId: string) => Promise<void>;
  onSkipActivity?: (activityId: string, reason?: string) => Promise<void>;
  onGetDirections?: (activityId: string) => void;
  onViewDetails?: (activityId: string) => void;
  isLoading?: boolean;
}

function ActivityCard({
  activity,
  type,
  className,
  onMarkCompleted,
  onStartActivity,
  onSkipActivity,
  onGetDirections,
  onViewDetails,
  isLoading = false
}: ActivityCardProps) {
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  if (!activity) {
    return (
      <div className={cn(
        "rounded-2xl p-6 border-2 border-dashed",
        "border-zinc-200 dark:border-zinc-800",
        "flex items-center justify-center",
        className
      )}>
        <div className="text-center text-zinc-500 dark:text-zinc-400">
          <Clock className="w-8 h-8 mx-auto mb-2" />
          <p className="text-sm">
            {type === 'current' ? 'Nenhuma atividade em andamento' : 'Próxima atividade não definida'}
          </p>
        </div>
      </div>
    );
  }

  const handleAction = async (action: string, handler?: (...args: any[]) => Promise<void>, ...args: any[]) => {
    if (!handler || actionLoading) return;

    setActionLoading(action);
    try {
      await handler(...args);
    } catch (error) {
      console.error(`Erro na ação ${action}:`, error);
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusBadge = () => {
    const status = activity.status || 'pending';
    const statusConfig = {
      pending: { label: 'Pendente', color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200' },
      in_progress: { label: 'Em Andamento', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' },
      completed: { label: 'Concluída', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
      skipped: { label: 'Pulada', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' }
    };

    const config = statusConfig[status];
    return (
      <div className={cn("px-3 py-1 rounded-full text-xs font-medium", config.color)}>
        {config.label}
      </div>
    );
  };

  return (
    <div className={cn(
      "relative h-full rounded-2xl p-6",
      "bg-gradient-to-br from-blue-50 to-indigo-50",
      "dark:from-blue-950/30 dark:to-indigo-950/30",
      "border border-blue-200 dark:border-blue-800",
      "hover:shadow-lg transition-all duration-300",
      type === 'current' && "ring-2 ring-blue-400 dark:ring-blue-600",
      activity.status === 'completed' && "from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border-green-200 dark:border-green-800",
      className
    )}>
      <div className="flex items-start justify-between mb-4">
        {getStatusBadge()}
        {activity.start_time && (
          <div className="text-sm text-zinc-500 dark:text-zinc-400">
            {formatTimeFromString(activity.start_time)}
          </div>
        )}
      </div>

      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-zinc-900 dark:text-white">
          {activity.title}
        </h3>

        {activity.description && (
          <p className="text-sm text-zinc-600 dark:text-zinc-300 line-clamp-2">
            {activity.description}
          </p>
        )}

        {activity.location_name && (
          <div className="flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400">
            <MapPin className="w-4 h-4" />
            <span>{activity.location_name}</span>
          </div>
        )}

        {/* Ações baseadas no status */}
        <div className="flex flex-wrap gap-2 pt-2">
          {activity.status === 'pending' && type === 'current' && onStartActivity && (
            <button
              onClick={() => handleAction('start', onStartActivity, activity.id)}
              disabled={actionLoading === 'start'}
              className={cn(
                "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                "bg-blue-600 text-white hover:bg-blue-700",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "transition-colors"
              )}
            >
              {actionLoading === 'start' ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              Iniciar
            </button>
          )}

          {activity.status === 'in_progress' && type === 'current' && onMarkCompleted && (
            <button
              onClick={() => handleAction('complete', onMarkCompleted, activity.id)}
              disabled={actionLoading === 'complete'}
              className={cn(
                "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                "bg-green-600 text-white hover:bg-green-700",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "transition-colors"
              )}
            >
              {actionLoading === 'complete' ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <CheckCircle className="w-4 h-4" />
              )}
              Concluir
            </button>
          )}

          {(activity.status === 'pending' || activity.status === 'in_progress') && onSkipActivity && (
            <button
              onClick={() => handleAction('skip', onSkipActivity, activity.id, 'Pulada pelo usuário')}
              disabled={actionLoading === 'skip'}
              className={cn(
                "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                "bg-orange-600 text-white hover:bg-orange-700",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "transition-colors"
              )}
            >
              {actionLoading === 'skip' ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <SkipForward className="w-4 h-4" />
              )}
              Pular
            </button>
          )}

          {activity.location_name && onGetDirections && (
            <button
              onClick={() => onGetDirections(activity.id)}
              className={cn(
                "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                "bg-white dark:bg-zinc-800 text-zinc-900 dark:text-white",
                "border border-zinc-200 dark:border-zinc-700",
                "hover:bg-zinc-50 dark:hover:bg-zinc-700",
                "transition-colors"
              )}
            >
              <Navigation className="w-4 h-4" />
              Direções
            </button>
          )}

          {onViewDetails && (
            <button
              onClick={() => onViewDetails(activity.id)}
              className={cn(
                "inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium",
                "bg-white dark:bg-zinc-800 text-zinc-900 dark:text-white",
                "border border-zinc-200 dark:border-zinc-700",
                "hover:bg-zinc-50 dark:hover:bg-zinc-700",
                "transition-colors"
              )}
            >
              <Eye className="w-4 h-4" />
              Detalhes
            </button>
          )}
        </div>

        {/* Informações de tracking */}
        {activity.status === 'completed' && activity.completed_at && (
          <div className="text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/30 p-2 rounded-lg">
            ✅ Concluída em {new Date(activity.completed_at).toLocaleString('pt-BR')}
            {activity.notes && <div className="mt-1 italic">"{activity.notes}"</div>}
          </div>
        )}

        {activity.status === 'in_progress' && activity.started_at && (
          <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-950/30 p-2 rounded-lg">
            🚀 Iniciada em {new Date(activity.started_at).toLocaleString('pt-BR')}
          </div>
        )}
      </div>
    </div>
  );
}

interface QuickStatsProps {
  dashboard: TravelModeDashboardType;
  onTabChange: (tab: string) => void;
}

function QuickStats({ dashboard, onTabChange }: QuickStatsProps) {
  const stats = [
    {
      title: "Gastos Hoje",
      value: dashboard.expense_summary?.today_spent ? `R$ ${dashboard.expense_summary.today_spent.toFixed(2)}` : "R$ 0,00",
      change: dashboard.expense_summary?.trend_percentage || 0,
      changeType: dashboard.expense_summary?.trend === 'up' ? 'negative' as const : 'positive' as const,
      icon: <DollarSign className="w-5 h-5 text-emerald-600" />,
      onClick: () => onTabChange('expenses')
    },
    {
      title: "Atividades Visitadas",
      value: dashboard.day_progress?.completed_activities || 0,
      change: 0,
      changeType: 'neutral' as const,
      icon: <CheckCircle className="w-5 h-5 text-blue-600" />,
      onClick: () => onTabChange('map')
    },
    {
      title: "Entradas no Diário",
      value: dashboard.diary_entries?.length || 0,
      change: 0,
      changeType: 'neutral' as const,
      icon: <BookOpen className="w-5 h-5 text-purple-600" />,
      onClick: () => onTabChange('diary')
    },
    {
      title: "Notificações",
      value: dashboard.notifications?.length || 0,
      change: 0,
      changeType: 'neutral' as const,
      icon: <Bell className="w-5 h-5 text-orange-600" />,
      onClick: () => onTabChange('notifications')
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <MetricCard
          key={index}
          title={stat.title}
          value={stat.value}
          change={stat.change}
          changeType={stat.changeType}
          icon={stat.icon}
          onClick={stat.onClick}
          className="group"
        />
      ))}
    </div>
  );
}

export default function TravelModeDashboard({ itineraryId, className }: TravelModeDashboardProps) {
  const [dashboard, setDashboard] = useState<TravelModeDashboardType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [sseEnabled, setSseEnabled] = useState(false);

  // Hook real-time moderno substituindo polling (só ativa após carregamento inicial)
  const {
    isConnected,
    reconnect,
    updateData
  } = useTravelModeRealtime(itineraryId, {
    enabled: sseEnabled,
    reconnectOnError: true,
    maxReconnectAttempts: 5
  });

  // Atualiza dados iniciais no hook de tempo real
  useEffect(() => {
    if (dashboard && updateData) {
      const locationData = dashboard.current_location ? {
        latitude: dashboard.current_location.lat,
        longitude: dashboard.current_location.lng,
        ...(dashboard.current_location.address && { address: dashboard.current_location.address })
      } : undefined;
      
      updateData({
        currentLocation: locationData
      });
    }
  }, [dashboard, updateData]);

  // Carregamento inicial via server action com fallback para API
  const loadInitialDashboard = async () => {
    try {
      setIsLoading(true);

      // Tentar server action primeiro
      try {
        const result = await getTravelModeDashboardAction(itineraryId);

        if (result.success) {
          setDashboard(result.data);
          setError(null);
          // Ativa SSE apenas após carregamento bem-sucedido e documento pronto
          if (document.readyState === 'complete') {
            setSseEnabled(true);
          } else {
            // Aguarda documento estar completamente carregado
            const handleLoad = () => {
              setSseEnabled(true);
              window.removeEventListener('load', handleLoad);
            };
            window.addEventListener('load', handleLoad);
          }
          return;
        } else {
          console.warn('Server action failed, trying API fallback:', result.error);
        }
      } catch (actionError) {
        console.warn('Server action error, trying API fallback:', actionError);
      }

      // Fallback para API
      const response = await fetch(`/api/travel-mode/${itineraryId}/dashboard`);
      const apiResult = await response.json();

      if (apiResult.success) {
        setDashboard(apiResult.data);
        setError(null);
        // Ativa SSE apenas após carregamento bem-sucedido e documento pronto
        if (document.readyState === 'complete') {
          // Delay adicional para garantir que tudo está estável
          setTimeout(() => setSseEnabled(true), 1500);
        } else {
          // Aguarda documento estar completamente carregado
          const handleLoad = () => {
            // Delay adicional após load para garantir estabilidade
            setTimeout(() => setSseEnabled(true), 2000);
            window.removeEventListener('load', handleLoad);
          };
          window.addEventListener('load', handleLoad);
        }
      } else {
        setError(apiResult.error || 'Erro ao carregar dashboard');
      }
    } catch (err) {
      console.error('Dashboard loading error:', err);
      setError('Erro ao carregar dashboard');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInitialDashboard();
  }, [itineraryId]);

  const handleRefresh = () => {
    loadInitialDashboard();
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  // Handlers para ações de atividades
  const handleMarkCompleted = async (activityId: string, notes?: string) => {
    try {
      console.log('🎯 Iniciando markCompleted:', { activityId, notes });

      const result = await markActivityCompletedAction(itineraryId, activityId, notes);

      console.log('📊 Resultado markCompleted:', result);

      if (result.success) {
        console.log('✅ Atividade marcada como concluída:', result.data);
        // Atualizar dashboard
        setTimeout(() => {
          loadInitialDashboard();
        }, 500);
      } else {
        console.error('❌ Erro ao marcar atividade:', result.error);
        alert(`Erro: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Erro inesperado ao marcar atividade:', error);
      alert('Erro inesperado ao marcar atividade como concluída');
    }
  };

  const handleStartActivity = async (activityId: string) => {
    try {
      console.log('🚀 Iniciando startActivity:', { activityId });

      const result = await startActivityAction(itineraryId, activityId);

      console.log('📊 Resultado startActivity:', result);

      if (result.success) {
        console.log('✅ Atividade iniciada:', result.data);
        // Atualizar dashboard
        setTimeout(() => {
          loadInitialDashboard();
        }, 500);
      } else {
        console.error('❌ Erro ao iniciar atividade:', result.error);
        alert(`Erro: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Erro inesperado ao iniciar atividade:', error);
      alert('Erro inesperado ao iniciar atividade');
    }
  };

  const handleSkipActivity = async (activityId: string, reason?: string) => {
    try {
      console.log('⏭️ Iniciando skipActivity:', { activityId, reason });

      const result = await skipActivityAction(itineraryId, activityId, reason);

      console.log('📊 Resultado skipActivity:', result);

      if (result.success) {
        console.log('✅ Atividade pulada:', result.data);
        // Atualizar dashboard
        setTimeout(() => {
          loadInitialDashboard();
        }, 500);
      } else {
        console.error('❌ Erro ao pular atividade:', result.error);
        alert(`Erro: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Erro inesperado ao pular atividade:', error);
      alert('Erro inesperado ao pular atividade');
    }
  };

  const handleGetDirections = (activityId: string) => {
    const activity = dashboard?.current_activity?.id === activityId
      ? dashboard.current_activity
      : dashboard?.next_activity;

    if (activity?.location_name) {
      // Abrir Google Maps ou Apple Maps
      const query = encodeURIComponent(activity.location_name);
      const url = `https://www.google.com/maps/search/?api=1&query=${query}`;
      window.open(url, '_blank');
    }
  };

  const handleViewDetails = (activityId: string) => {
    // Por enquanto, apenas log - pode ser expandido para modal ou página
    console.log('Ver detalhes da atividade:', activityId);
    alert('Funcionalidade de detalhes será implementada em breve!');
  };

  if (isLoading) {
    return (
      <div className={cn(
        "flex items-center justify-center min-h-[400px]",
        className
      )}>
        <div className="flex items-center gap-3 text-zinc-500">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>Carregando dashboard...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn(
        "flex items-center justify-center min-h-[400px]",
        className
      )}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-zinc-900 dark:text-white mb-2">
            Erro ao carregar dashboard
          </h3>
          <p className="text-zinc-500 dark:text-zinc-400 mb-4">
            {error}
          </p>
          <button
            onClick={handleRefresh}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  if (!dashboard) {
    return null;
  }

  return (
    <div className={cn("w-full space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">
            Modo Viagem
          </h1>
          <p className="text-zinc-500 dark:text-zinc-400 mt-1">
            Dashboard em tempo real da sua viagem
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* Status de conexão real-time */}
          <div className={cn(
            "flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium",
            isConnected 
              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
          )}>
            <div className={cn(
              "w-2 h-2 rounded-full",
              isConnected ? "bg-green-500" : "bg-red-500"
            )} />
            {isConnected ? 'Tempo Real' : 'Desconectado'}
          </div>
          
          <button
            onClick={isConnected ? reconnect : loadInitialDashboard}
            className={cn(
              "inline-flex items-center gap-2 px-4 py-2 rounded-lg",
              "bg-zinc-100 dark:bg-zinc-800 text-zinc-900 dark:text-white",
              "hover:bg-zinc-200 dark:hover:bg-zinc-700",
              "transition-colors border border-zinc-200 dark:border-zinc-700"
            )}
          >
            <RefreshCw className="w-4 h-4" />
            {isConnected ? 'Reconectar' : 'Atualizar'}
          </button>
        </div>
      </div>

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6 lg:grid-cols-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            <span className="hidden sm:inline">Visão Geral</span>
          </TabsTrigger>
          <TabsTrigger value="map" className="flex items-center gap-2">
            <Map className="w-4 h-4" />
            <span className="hidden sm:inline">Mapa</span>
          </TabsTrigger>
          <TabsTrigger value="expenses" className="flex items-center gap-2">
            <CreditCard className="w-4 h-4" />
            <span className="hidden sm:inline">Gastos</span>
          </TabsTrigger>
          <TabsTrigger value="diary" className="flex items-center gap-2">
            <BookOpen className="w-4 h-4" />
            <span className="hidden sm:inline">Diário</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="w-4 h-4" />
            <span className="hidden sm:inline">Alertas</span>
          </TabsTrigger>
          <TabsTrigger value="assistant" className="flex items-center gap-2">
            <Bot className="w-4 h-4" />
            <span className="hidden sm:inline">Assistente</span>
          </TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <QuickStats dashboard={dashboard} onTabChange={handleTabChange} />
            
            {/* Current & Next Activities */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-zinc-900 dark:text-white flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Atividade Atual
                </h2>
                                 <ActivityCard
                   activity={dashboard.current_activity}
                   type="current"
                   onMarkCompleted={handleMarkCompleted}
                   onStartActivity={handleStartActivity}
                   onSkipActivity={handleSkipActivity}
                   onGetDirections={handleGetDirections}
                   onViewDetails={handleViewDetails}
                   isLoading={isLoading}
                 />
               </div>

               <div className="space-y-4">
                 <h2 className="text-xl font-semibold text-zinc-900 dark:text-white flex items-center gap-2">
                   <Calendar className="w-5 h-5" />
                   Próxima Atividade
                 </h2>
                 <ActivityCard
                   activity={dashboard.next_activity}
                   type="next"
                   onGetDirections={handleGetDirections}
                   onViewDetails={handleViewDetails}
                   isLoading={isLoading}
                 />
              </div>
            </div>

            {/* Weather Summary */}
            {dashboard.weather && (
              <div className="rounded-2xl p-6 bg-gradient-to-r from-sky-50 to-blue-50 dark:from-sky-950/30 dark:to-blue-950/30 border border-sky-200 dark:border-sky-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-full bg-sky-100 dark:bg-sky-900/50">
                      <CloudSun className="w-6 h-6 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-zinc-900 dark:text-white">
                        {dashboard.weather.description}
                      </h3>
                                             <p className="text-sky-600 dark:text-sky-400">
                         {dashboard.weather.temperature}°C • {dashboard.weather.description}
                       </p>
                    </div>
                  </div>
                  <div className="text-right text-sm text-zinc-500 dark:text-zinc-400">
                    <p>Umidade: {dashboard.weather.humidity}%</p>
                    <p>Vento: {dashboard.weather.windSpeed} km/h</p>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="map">
            <TravelModeMap
              {...(dashboard.current_location && { currentLocation: dashboard.current_location })}
              activities={[
                ...(dashboard.current_activity ? [{
                  id: dashboard.current_activity.id,
                  title: dashboard.current_activity.title,
                  location: {
                    lat: -23.5505, // Mock data - replace with real location
                    lng: -46.6333,
                    address: dashboard.current_activity.location_name || 'Localização atual'
                  },
                  status: 'current' as const
                }] : []),
                ...(dashboard.next_activity ? [{
                  id: dashboard.next_activity.id,
                  title: dashboard.next_activity.title,
                  location: {
                    lat: -23.5515, // Mock data - replace with real location  
                    lng: -46.6343,
                    address: dashboard.next_activity.location_name || 'Próximo destino'
                  },
                  status: 'upcoming' as const
                }] : [])
              ]}
              alternatives={dashboard.alternative_activities}
            />
          </TabsContent>

          <TabsContent value="expenses">
            <TravelModeExpenses
              itineraryId={itineraryId}
              expenses={[]}
              categories={dashboard.expense_categories || []}
              splits={dashboard.expense_splits || []}
              stats={dashboard.expense_summary}
              onRefresh={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="diary">
            <TravelModeDiary
              entries={dashboard.diary_entries || []}
              currentLocation={dashboard.current_location}
              currentWeather={dashboard.weather?.condition}
              onRefresh={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="notifications">
            <TravelModeNotifications
              itineraryId={itineraryId}
              notifications={dashboard.notifications}
              onNotificationUpdate={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="assistant">
            <TravelModeAssistant
              dashboard={dashboard}
              currentLocation={dashboard.current_location}
              weather={dashboard.weather}
              onRefresh={handleRefresh}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
} 
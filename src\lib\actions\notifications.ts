'use server';

import { createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import type { SocialNotification, SocialNotificationType, NotificationGroup } from '@/types/social';

// =====================================================
// NOTIFICATION ACTIONS - SISTEMA SOCIAL MODERNO
// =====================================================

interface NotificationActionResult {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Busca notificações do usuário com agrupamento inteligente
 */
export async function getUserNotifications(): Promise<{
  success: boolean;
  data?: {
    notifications: SocialNotification[];
    grouped: NotificationGroup[];
    unreadCount: number;
  };
  error?: string;
}> {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Usuário não autenticado' };
    }

    // Buscar notificações com dados do ator
    const { data: notifications, error: notifError } = await supabase
      .from('social_notifications')
      .select(`
        *,
        actor:actor_id (
          id,
          username,
          display_name,
          avatar_url,
          verified
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(50);

    if (notifError) {
      console.error('Erro ao buscar notificações:', notifError);
      return { success: false, error: 'Erro ao carregar notificações' };
    }

    const typedNotifications = notifications as SocialNotification[];

    // Contar não lidas
    const unreadCount = typedNotifications.filter(n => !n.is_read).length;

    // Agrupar notificações similares (mesmo tipo + mesmo post nas últimas 24h)
    const grouped = groupNotifications(typedNotifications);

    return {
      success: true,
      data: {
        notifications: typedNotifications,
        grouped,
        unreadCount
      }
    };

  } catch (error) {
    console.error('Erro na busca de notificações:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Cria uma nova notificação social
 */
export async function createSocialNotification(
  targetUserId: string,
  actorId: string,
  type: SocialNotificationType,
  options: {
    postId?: string;
    commentId?: string;
    storyId?: string;
    content?: string;
  } = {}
): Promise<NotificationActionResult> {
  try {
    const supabase = await createClient();

    // Não notificar a si mesmo
    if (targetUserId === actorId) {
      return { success: true };
    }

    // Verificar se já existe notificação similar recente (evitar spam)
    const { data: existing } = await supabase
      .from('social_notifications')
      .select('id')
      .eq('user_id', targetUserId)
      .eq('actor_id', actorId)
      .eq('type', type)
      .eq('post_id', options.postId || null)
      .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // 5 minutos
      .single();

    if (existing) {
      return { success: true }; // Já existe, não criar duplicata
    }

    // Criar notificação
    const { data: notification, error } = await supabase
      .from('social_notifications')
      .insert({
        user_id: targetUserId,
        actor_id: actorId,
        type,
        post_id: options.postId || null,
        comment_id: options.commentId || null,
        story_id: options.storyId || null,
        content: options.content || null,
        is_read: false
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar notificação:', error);
      return { success: false, error: 'Erro ao criar notificação' };
    }

    return { success: true, data: notification };

  } catch (error) {
    console.error('Erro na criação de notificação:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Marca notificação como lida
 */
export async function markNotificationAsRead(notificationId: string): Promise<NotificationActionResult> {
  try {
    const supabase = await createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Usuário não autenticado' };
    }

    const { error } = await supabase
      .from('social_notifications')
      .update({ is_read: true })
      .eq('id', notificationId)
      .eq('user_id', user.id); // Segurança: só pode marcar suas próprias notificações

    if (error) {
      console.error('Erro ao marcar notificação como lida:', error);
      return { success: false, error: 'Erro ao atualizar notificação' };
    }

    revalidatePath('/feed');
    return { success: true };

  } catch (error) {
    console.error('Erro ao marcar notificação:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Marca todas as notificações como lidas
 */
export async function markAllNotificationsAsRead(): Promise<NotificationActionResult> {
  try {
    const supabase = await createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Usuário não autenticado' };
    }

    const { error } = await supabase
      .from('social_notifications')
      .update({ is_read: true })
      .eq('user_id', user.id)
      .eq('is_read', false);

    if (error) {
      console.error('Erro ao marcar todas como lidas:', error);
      return { success: false, error: 'Erro ao atualizar notificações' };
    }

    revalidatePath('/feed');
    return { success: true };

  } catch (error) {
    console.error('Erro ao marcar todas:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Agrupa notificações similares para melhor UX
 */
function groupNotifications(notifications: SocialNotification[]): NotificationGroup[] {
  const groups: { [key: string]: NotificationGroup } = {};
  const now = new Date();
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  notifications.forEach(notification => {
    const notifDate = new Date(notification.created_at);
    
    // Só agrupar notificações das últimas 24h
    if (notifDate < oneDayAgo) return;

    // Chave para agrupamento: tipo + post_id (se existir)
    const groupKey = `${notification.type}_${notification.post_id || 'no_post'}`;
    
    if (!groups[groupKey]) {
      groups[groupKey] = {
        type: notification.type,
        count: 1,
        latest_actor: notification.actor,
        other_actors: [],
        post_id: notification.post_id,
        created_at: notification.created_at,
        is_read: notification.is_read
      };
    } else {
      groups[groupKey].count++;
      
      // Adicionar ator se não estiver na lista
      const actorExists = groups[groupKey].other_actors.some(a => a.id === notification.actor.id) ||
                         groups[groupKey].latest_actor.id === notification.actor.id;
      
      if (!actorExists) {
        groups[groupKey].other_actors.push(notification.actor);
      }
      
      // Manter a data mais recente
      if (new Date(notification.created_at) > new Date(groups[groupKey].created_at)) {
        groups[groupKey].created_at = notification.created_at;
        groups[groupKey].latest_actor = notification.actor;
      }
      
      // Se qualquer notificação não foi lida, o grupo não foi lido
      if (!notification.is_read) {
        groups[groupKey].is_read = false;
      }
    }
  });

  return Object.values(groups).sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );
}

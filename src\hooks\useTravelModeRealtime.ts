"use client";

import { useEffect, useRef, useState, useCallback } from 'react';

// Interface para dados do Travel Mode
interface TravelModeData {
  currentLocation?: {
    latitude: number;
    longitude: number;
    address?: string;
    accuracy?: number;
  } | undefined;
  weather?: {
    temperature: number;
    description: string;
    humidity: number;
    windSpeed: number;
  } | undefined;
  route?: {
    distance: number;
    duration: number;
    instructions: string[];
  } | undefined;
  currentActivity?: {
    id: string;
    title: string;
    status: string;
  } | undefined;
  activities?: Array<{
    id: string;
    title: string;
    status: string;
  }> | undefined;
}

interface RealtimeOptions {
  enabled?: boolean;
  reconnectOnError?: boolean;
  maxReconnectAttempts?: number;
  initialReconnectDelay?: number;
  maxReconnectDelay?: number;
  heartbeatInterval?: number;
}

interface RealtimeState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  lastUpdate: Date | null;
  reconnectAttempts: number;
}

interface TravelModeRealtimeUpdate {
  type: 'location' | 'weather' | 'route' | 'activity' | 'notification' | 'heartbeat' | 'connected' | 'dashboard';
  data: any;
  timestamp: number;
}

export function useTravelModeRealtime(
  itineraryId: string,
  options: RealtimeOptions = {}
) {
  const {
    enabled = true,
    reconnectOnError = true,
    maxReconnectAttempts = 5,
    initialReconnectDelay = 1000,
    maxReconnectDelay = 30000,
    heartbeatInterval = 30000
  } = options;

  const [state, setState] = useState<RealtimeState>({
    connected: false,
    connecting: false,
    error: null,
    lastUpdate: null,
    reconnectAttempts: 0
  });

  const [data, setData] = useState<TravelModeData | null>(null);
  
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectDelayRef = useRef(initialReconnectDelay);

  /**
   * Atualiza estado da conexão
   */
  const updateState = useCallback((updates: Partial<RealtimeState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * Calcula próximo delay de reconexão (exponential backoff)
   */
  const getNextReconnectDelay = useCallback(() => {
    const delay = Math.min(
      reconnectDelayRef.current * (1 + Math.random()), // Jitter
      maxReconnectDelay
    );
    reconnectDelayRef.current = Math.min(reconnectDelayRef.current * 2, maxReconnectDelay);
    return delay;
  }, [maxReconnectDelay]);

  /**
   * Reset delay de reconexão após conexão bem-sucedida
   */
  const resetReconnectDelay = useCallback(() => {
    reconnectDelayRef.current = initialReconnectDelay;
  }, [initialReconnectDelay]);

  /**
   * Setup heartbeat para detectar conexões mortas
   */
  const setupHeartbeat = useCallback(() => {
    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
    }

    heartbeatTimeoutRef.current = setTimeout(() => {
      console.warn('Heartbeat timeout - reconectando...');
      connect();
    }, heartbeatInterval + 5000); // 5s de tolerância
  }, [heartbeatInterval]);

  /**
   * Processa atualizações recebidas via SSE
   */
  const handleUpdate = useCallback((update: TravelModeRealtimeUpdate) => {
    updateState({ 
      lastUpdate: new Date(),
      error: null 
    });

    // Reset heartbeat timer
    setupHeartbeat();

    switch (update.type) {
      case 'heartbeat':
        // Apenas mantém conexão viva
        break;
        
      case 'connected':
        console.log('🔗 SSE conectado:', update.data?.message || 'Conexão estabelecida');
        break;
        
      case 'dashboard':
        // Atualiza dados completos do dashboard
        if (update.data?.success) {
          setData(prev => ({
            currentLocation: update.data.current_activity?.location_name ? {
              latitude: -23.5505,
              longitude: -46.6333,
              address: update.data.current_activity.location_name
            } : prev?.currentLocation,
            weather: update.data.weather,
            route: update.data.route,
            currentActivity: update.data.current_activity,
            activities: update.data.alternative_activities || prev?.activities
          }));
        }
        break;
        
      case 'location':
        setData(prev => prev ? {
          ...prev,
          currentLocation: update.data
        } : null);
        break;
        
      case 'weather':
        setData(prev => prev ? {
          ...prev,
          weather: update.data
        } : null);
        break;
        
      case 'route':
        setData(prev => prev ? {
          ...prev,
          route: update.data
        } : null);
        break;
        
      case 'activity':
        setData(prev => prev ? {
          ...prev,
          currentActivity: update.data.currentActivity,
          activities: update.data.activities || prev.activities
        } : null);
        break;
        
      case 'notification':
        // Dispatch custom event para notificações
        window.dispatchEvent(new CustomEvent('travel-mode-notification', {
          detail: update.data
        }));
        break;
        
      default:
        console.warn('Tipo de update desconhecido:', update.type);
    }
  }, [updateState, setupHeartbeat]);

  /**
   * Verifica se o servidor está respondendo
   */
  const checkServerHealth = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch(`/api/travel-mode/${itineraryId}/dashboard`, {
        method: 'HEAD', // Só verifica se responde, sem baixar dados
        signal: AbortSignal.timeout(5000) // Timeout de 5s
      });
      return response.ok;
    } catch {
      return false;
    }
  }, [itineraryId]);

  /**
   * Conecta ao SSE endpoint
   */
  const connect = useCallback(async () => {
    if (!enabled || !itineraryId) return;

    // Evita conexão durante navegação
    if (document.readyState !== 'complete') {
      // Agenda nova tentativa após documento estar pronto
      const handleLoad = () => {
        connect();
        window.removeEventListener('load', handleLoad);
      };
      window.addEventListener('load', handleLoad);
      return;
    }

    // Verifica se servidor está respondendo antes de conectar SSE
    const serverHealthy = await checkServerHealth();
    if (!serverHealthy) {
      // Se servidor não está saudável, agenda nova tentativa
      if (state.reconnectAttempts < maxReconnectAttempts) {
        setState(prev => ({
          ...prev,
          reconnectAttempts: prev.reconnectAttempts + 1
        }));

        setTimeout(() => connect(), 3000);
      }
      return;
    }

    // Fecha conexão existente
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    // Limpa timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    updateState({
      connecting: true,
      error: null
    });

    try {
      const eventSource = new EventSource(`/api/travel-mode/${itineraryId}/sse`);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('✅ Conexão SSE estabelecida');
        updateState({
          connected: true,
          connecting: false,
          error: null,
          reconnectAttempts: 0
        });
        resetReconnectDelay();
        setupHeartbeat();
      };

      eventSource.onmessage = (event) => {
        try {
          const update: TravelModeRealtimeUpdate = JSON.parse(event.data);
          handleUpdate(update);
        } catch (error) {
          console.error('Erro ao parsear SSE data:', error);
        }
      };

      eventSource.onerror = (error) => {
        // Verifica diferentes tipos de erro
        const isNavigationError = eventSource.readyState === EventSource.CLOSED;
        const isConnectionError = eventSource.readyState === EventSource.CONNECTING;

        // Só loga erros que não são de navegação ou reinicialização
        if (!isNavigationError && !isConnectionError) {
          console.error('❌ Erro na conexão SSE:', error);
        }

        updateState({
          connected: false,
          connecting: false,
          error: (isNavigationError || isConnectionError) ? null : 'Erro de conexão'
        });

        // Limpa heartbeat
        if (heartbeatTimeoutRef.current) {
          clearTimeout(heartbeatTimeoutRef.current);
          heartbeatTimeoutRef.current = null;
        }

        // Só tenta reconectar se não for erro de navegação e se habilitado
        if (!isNavigationError && reconnectOnError && state.reconnectAttempts < maxReconnectAttempts) {
          const delay = getNextReconnectDelay();

          setState(prev => ({
            ...prev,
            reconnectAttempts: prev.reconnectAttempts + 1
          }));

          // Para erros de conexão (servidor reiniciando), usa delay maior
          const reconnectDelay = isConnectionError ? Math.max(delay, 3000) : delay;

          if (!isConnectionError) {
            console.log(`🔄 Tentando reconectar em ${reconnectDelay}ms (tentativa ${state.reconnectAttempts + 1}/${maxReconnectAttempts})`);
          }

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectDelay);
        } else if (!isNavigationError && !isConnectionError) {
          updateState({
            error: 'Falha na conexão após múltiplas tentativas'
          });
        }
      };

    } catch (error) {
      console.error('Erro ao criar EventSource:', error);
      updateState({
        connected: false,
        connecting: false,
        error: 'Erro ao estabelecer conexão'
      });
    }
  }, [
    enabled,
    itineraryId,
    checkServerHealth,
    state.reconnectAttempts,
    maxReconnectAttempts,
    setState,
    reconnectOnError,
    updateState,
    getNextReconnectDelay,
    resetReconnectDelay,
    setupHeartbeat,
    handleUpdate
  ]);

  /**
   * Desconecta do SSE
   */
  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }

    updateState({
      connected: false,
      connecting: false,
      error: null,
      reconnectAttempts: 0
    });
  }, [updateState]);

  /**
   * Força reconexão manual
   */
  const reconnect = useCallback(() => {
    updateState({ reconnectAttempts: 0 });
    resetReconnectDelay();
    connect();
  }, [updateState, resetReconnectDelay, connect]);

  /**
   * Atualiza dados iniciais
   */
  const updateData = useCallback((newData: TravelModeData) => {
    setData(newData);
  }, []);

  // Conecta automaticamente quando habilitado (com delay e verificação de servidor)
  useEffect(() => {
    if (enabled && itineraryId) {
      // Verifica se estamos em uma navegação ativa
      const isNavigating = document.readyState !== 'complete';

      // Delay maior se estamos navegando ou se é uma reinicialização
      const delay = isNavigating ? 2000 : 1000;

      const timer = setTimeout(async () => {
        // Verifica novamente se ainda estamos habilitados (componente pode ter desmontado)
        if (!enabled) return;

        // Verifica se o servidor está respondendo antes de conectar SSE
        try {
          const healthCheck = await fetch(`/api/travel-mode/${itineraryId}/dashboard`);
          if (healthCheck.ok) {
            // Verifica uma última vez se ainda estamos habilitados
            if (enabled) {
              connect();
            }
          } else {
            // Se servidor não está pronto, tenta novamente em 3s
            if (enabled) {
              setTimeout(() => connect(), 3000);
            }
          }
        } catch (error) {
          // Se falhou, tenta novamente em 3s
          if (enabled) {
            setTimeout(() => connect(), 3000);
          }
        }
      }, delay);

      return () => {
        clearTimeout(timer);
        disconnect();
      };
    } else {
      disconnect();
    }
  }, [enabled, itineraryId, connect, disconnect]);

  // Cleanup na desmontagem
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    // Estado da conexão
    ...state,
    
    // Dados em tempo real
    data,
    
    // Controles
    connect,
    disconnect,
    reconnect,
    updateData,
    
    // Status helpers
    isConnected: state.connected,
    isConnecting: state.connecting,
    hasError: !!state.error,
    canReconnect: state.reconnectAttempts < maxReconnectAttempts,
    
    // Métricas
    uptime: state.lastUpdate ? Date.now() - state.lastUpdate.getTime() : 0
  };
} 
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { weatherService } from '@/lib/services/weather'

interface TravelModeRealtimeUpdate {
  type: 'connected' | 'heartbeat' | 'location' | 'dashboard' | 'weather' | 'route'
  data?: any
  timestamp: string
}

interface LocationUpdate {
  lat: number
  lng: number
  accuracy: number
  address?: string | undefined
  timestamp: string
  speed?: number | undefined
  heading?: number | undefined
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: itineraryId } = await params

  console.log('🔌 SSE Connection requested for itinerary:', itineraryId)

  // Verificar autenticação
  const supabase = await createClient()
  
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('SSE Auth error:', authError)
      return new NextResponse('Unauthorized', { status: 401 })
    }

    console.log('✅ SSE User authenticated:', user.id)

    // Verificar se há dados de travel mode para este usuário (mais direto)
    const { data: liveData, error: liveDataError } = await supabase
      .from('travel_mode_live_data')
      .select('id, itinerary_id, user_id')
      .eq('itinerary_id', itineraryId)
      .eq('user_id', user.id)
      .single()

    if (liveDataError) {
      console.error('SSE Live data error:', liveDataError)

      // Se não encontrou dados, tentar ativar automaticamente
      if (liveDataError.code === 'PGRST116') { // Not found
        console.log('🔄 Tentando ativar travel mode automaticamente...')

        try {
          const { data: activationResult, error: activationError } = await supabase
            .rpc('auto_activate_travel_mode', { p_itinerary_id: itineraryId })

          if (activationError || !activationResult?.success) {
            console.error('❌ Auto activation failed:', activationError || activationResult)
            return new NextResponse('Travel mode not activated', { status: 404 })
          }

          console.log('✅ Travel mode auto-activated successfully')
        } catch (autoActivateError) {
          console.error('❌ Auto activation exception:', autoActivateError)
          return new NextResponse('Failed to activate travel mode', { status: 500 })
        }
      } else {
        return new NextResponse('Error accessing travel mode data', { status: 500 })
      }
    }

    console.log('✅ SSE Live data found:', liveData?.id)

    // Configurar SSE
    const encoder = new TextEncoder()
    let isClosed = false
    
    const stream = new ReadableStream({
      start(controller) {
        const sendUpdate = (update: TravelModeRealtimeUpdate) => {
          if (isClosed) return
          
          try {
            const data = `data: ${JSON.stringify(update)}\n\n`
            controller.enqueue(encoder.encode(data))
          } catch (error) {
            console.error('Error sending SSE update:', error)
          }
        }

        const sendHeartbeat = () => {
          sendUpdate({
            type: 'heartbeat',
            timestamp: new Date().toISOString(),
          })
        }

        const sendLocationUpdate = async () => {
          try {
            // Obter localização atual (se disponível no servidor, usar fallback)
            const { data: liveData } = await supabase
              .from('travel_mode_live_data')
              .select('current_location')
              .eq('itinerary_id', itineraryId)
              .eq('user_id', user.id)
              .single()

            if (liveData?.current_location) {
              const location = liveData.current_location as LocationUpdate
              
              // Simular variação realista na localização (drift GPS)
              const driftLat = (Math.random() - 0.5) * 0.0001 // ~10m de drift
              const driftLng = (Math.random() - 0.5) * 0.0001
              
              // Atualizar com timestamp atual e pequeno drift
              const locationUpdate: LocationUpdate = {
                ...location,
                lat: location.lat + driftLat,
                lng: location.lng + driftLng,
                accuracy: Math.max(5, location.accuracy + Math.floor(Math.random() * 10)), 
                timestamp: new Date().toISOString(),
                // Simular velocidade se em movimento
                speed: Math.random() > 0.7 ? Math.random() * 2 : undefined, // 0-2 m/s
                heading: Math.random() > 0.8 ? Math.floor(Math.random() * 360) : undefined,
              }

              sendUpdate({
                type: 'location',
                data: locationUpdate,
                timestamp: new Date().toISOString(),
              })
            }
          } catch (error) {
            console.error('Error sending location update:', error)
          }
        }

        const sendWeatherUpdate = async () => {
          try {
            const { data: liveData } = await supabase
              .from('travel_mode_live_data')
              .select('current_location')
              .eq('itinerary_id', itineraryId)
              .eq('user_id', user.id)
              .single()

            if (liveData?.current_location) {
              const location = liveData.current_location as { lat: number; lng: number }
              
              // Obter clima real
              const weatherData = await weatherService.getCurrentWeather(location.lat, location.lng)
              
              if (weatherData) {
                sendUpdate({
                  type: 'weather',
                  data: {
                    temperature: weatherData.temperature,
                    description: weatherData.description,
                    icon: weatherData.icon,
                    humidity: weatherData.humidity,
                    windSpeed: weatherData.windSpeed,
                    feelsLike: weatherData.feelsLike,
                    pressure: weatherData.pressure,
                    uvIndex: weatherData.uvIndex,
                    cloudCover: weatherData.cloudCover,
                    precipProbability: weatherData.precipProbability,
                    alerts: weatherData.alerts,
                    location: weatherData.location,
                    timestamp: new Date().toISOString(),
                  },
                  timestamp: new Date().toISOString(),
                })
              }
            }
          } catch (error) {
            console.error('Error sending weather update:', error)
            
            // Fallback: clima dinâmico baseado na hora do dia
            const hour = new Date().getHours()
            const isDay = hour >= 6 && hour < 18
            const descriptions = ['Ensolarado', 'Parcialmente nublado', 'Nublado', 'Chuvisco']
            const icons = isDay ? ['01d', '02d', '03d', '10d'] : ['01n', '02n', '03n', '10n']
            const randomIndex = Math.floor(Math.random() * descriptions.length)
            
            sendUpdate({
              type: 'weather',
              data: {
                temperature: 18 + Math.floor(Math.random() * 15), // 18-32°C
                description: descriptions[randomIndex],
                icon: icons[randomIndex],
                humidity: 45 + Math.floor(Math.random() * 40), // 45-85%
                windSpeed: 3 + Math.floor(Math.random() * 20), // 3-23 km/h
                feelsLike: 16 + Math.floor(Math.random() * 18), // 16-34°C
                pressure: 1005 + Math.floor(Math.random() * 20), // 1005-1025 hPa
                timestamp: new Date().toISOString(),
              },
              timestamp: new Date().toISOString(),
            })
          }
        }

        const sendDashboardUpdate = async () => {
          try {
            // Usar função real de dashboard
            const { data: dashboardData, error: dashboardError } = await supabase
              .rpc('get_travel_mode_dashboard_real', { 
                p_itinerary_id: itineraryId,
                p_user_id: user.id 
              })

            if (dashboardError) {
              console.error('Dashboard RPC error:', dashboardError)
              return
            }

            if (dashboardData?.success) {
              sendUpdate({
                type: 'dashboard',
                data: dashboardData,
                timestamp: new Date().toISOString(),
              })
            }
          } catch (error) {
            console.error('Error sending dashboard update:', error)
          }
        }

        const sendRouteUpdate = async () => {
          try {
            const { data: liveData } = await supabase
              .from('travel_mode_live_data')
              .select('route_data, current_location, next_activity_id')
              .eq('itinerary_id', itineraryId)
              .eq('user_id', user.id)
              .single()

            if (liveData?.route_data) {
              // Calcular tempo de viagem atualizado baseado na localização atual
              const routeData = liveData.route_data as any
              const currentLocation = liveData.current_location as { lat: number; lng: number }
              
              // Se há próxima atividade, obter sua localização
              if (liveData.next_activity_id) {
                const { data: nextActivity } = await supabase
                  .from('itinerary_activities')
                  .select('location_data')
                  .eq('id', liveData.next_activity_id)
                  .single()

                if (nextActivity?.location_data) {
                  const nextLocation = nextActivity.location_data as { lat: number; lng: number }
                  
                  // Calcular distância aproximada
                  const distance = calculateDistance(currentLocation, nextLocation)
                  
                  const updatedRoute = {
                    ...routeData,
                    distance_remaining: Math.max(0, distance),
                    estimated_arrival: new Date(Date.now() + (distance / 4) * 60 * 1000).toISOString(), // 4 km/h velocidade caminhada
                    updated_at: new Date().toISOString(),
                  }

                  sendUpdate({
                    type: 'route',
                    data: updatedRoute,
                    timestamp: new Date().toISOString(),
                  })
                }
              }
            }
          } catch (error) {
            console.error('Error sending route update:', error)
          }
        }

        // Função auxiliar para calcular distância
        const calculateDistance = (pos1: { lat: number; lng: number }, pos2: { lat: number; lng: number }): number => {
          const R = 6371000 // Raio da Terra em metros
          const dLat = (pos2.lat - pos1.lat) * Math.PI / 180
          const dLng = (pos2.lng - pos1.lng) * Math.PI / 180
          const a = 
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(pos1.lat * Math.PI / 180) * Math.cos(pos2.lat * Math.PI / 180) *
            Math.sin(dLng / 2) * Math.sin(dLng / 2)
          const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
          return R * c
        }

        // Configurar Supabase Realtime para mudanças no banco
        const channel = supabase
          .channel(`travel_mode_${itineraryId}`)
          .on(
            'postgres_changes',
            {
              event: 'UPDATE',
              schema: 'public',
              table: 'travel_mode_live_data',
              filter: `itinerary_id=eq.${itineraryId}`,
            },
                         (payload: any) => {
              console.log('Database change detected:', payload)
              
              // Enviar atualizações quando dados mudarem
              setTimeout(() => {
                sendDashboardUpdate()
                sendLocationUpdate()
                sendWeatherUpdate()
                sendRouteUpdate()
              }, 100)
            }
          )
          .subscribe()

        // Health check function para validar conectividade
        const performHealthCheck = async () => {
          try {
            const { data, error } = await supabase
              .from('travel_mode_live_data')
              .select('id')
              .eq('itinerary_id', itineraryId)
              .limit(1)
              .single()
            
            if (error || !data) {
              console.warn('Health check failed:', error)
              // Tentar reconectar Supabase se necessário
              return false
            }
            return true
          } catch (error) {
            console.error('Health check error:', error)
            return false
          }
        }

        // Error recovery function
        const attemptRecovery = async () => {
          console.log('Attempting SSE recovery...')
          
          try {
            // Verificar se ainda estamos conectados
            if (isClosed) return
            
            // Tentar reenviar dados básicos
            await sendDashboardUpdate()
            await sendLocationUpdate()
            
            console.log('SSE recovery successful')
          } catch (error) {
            console.error('SSE recovery failed:', error)
          }
        }

        // Enviar connected com informações do sistema
        sendUpdate({
          type: 'connected',
          data: {
            server_time: new Date().toISOString(),
            connection_id: crypto.randomUUID(),
            intervals: {
              heartbeat: 30000,
              location: 15000,
              weather: 300000,
              dashboard: 60000,
              route: 45000
            }
          },
          timestamp: new Date().toISOString(),
        })

        // Configurar intervalos com error handling
        const heartbeatInterval = setInterval(() => {
          try {
            sendHeartbeat()
          } catch (error) {
            console.error('Heartbeat error:', error)
          }
        }, 30000) // 30s
        
        const locationInterval = setInterval(() => {
          sendLocationUpdate().catch(console.error)
        }, 15000) // 15s  
        
        const weatherInterval = setInterval(() => {
          sendWeatherUpdate().catch(console.error)
        }, 300000) // 5min
        
        const dashboardInterval = setInterval(() => {
          sendDashboardUpdate().catch(console.error)
        }, 60000) // 1min
        
        const routeInterval = setInterval(() => {
          sendRouteUpdate().catch(console.error)
        }, 45000) // 45s

        // Health check interval (verificar a cada 2 minutos)
        const healthCheckInterval = setInterval(async () => {
          const isHealthy = await performHealthCheck()
          if (!isHealthy) {
            await attemptRecovery()
          }
        }, 120000) // 2min

        // Enviar dados iniciais com delay escalonado
        setTimeout(() => sendDashboardUpdate().catch(console.error), 500)
        setTimeout(() => sendLocationUpdate().catch(console.error), 1000)
        setTimeout(() => sendWeatherUpdate().catch(console.error), 1500)
        setTimeout(() => sendRouteUpdate().catch(console.error), 2000)

        // Cleanup function
        const cleanup = () => {
          isClosed = true
          clearInterval(heartbeatInterval)
          clearInterval(locationInterval)
          clearInterval(weatherInterval)
          clearInterval(dashboardInterval)
          clearInterval(routeInterval)
          clearInterval(healthCheckInterval)
          
          try {
            supabase.removeChannel(channel)
          } catch (error) {
            console.error('Error removing Supabase channel:', error)
          }
          
          try {
            controller.close()
          } catch (error) {
            console.error('Error closing controller:', error)
          }
          
          console.log('SSE connection cleaned up for itinerary:', itineraryId)
        }

        // Handle client disconnect
        request.signal.addEventListener('abort', cleanup)
        
        // Handle stream cancellation
        return cleanup
      },
      cancel() {
        isClosed = true
      }
    })

    return new NextResponse(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Cache-Control, Authorization',
        'X-Accel-Buffering': 'no', // Nginx
        'X-Content-Type-Options': 'nosniff',
      },
    })

  } catch (error) {
    console.error('SSE endpoint error:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
} 
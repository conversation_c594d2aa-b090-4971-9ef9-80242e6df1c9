'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';
import { getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '@/lib/actions/notifications';
import type { SocialNotification, NotificationGroup, UseNotificationsReturn } from '@/types/social';

// =====================================================
// HOOK DE NOTIFICAÇÕES SOCIAIS - MODERNO E EFICIENTE
// =====================================================

export function useNotifications(): UseNotificationsReturn {
  const [notifications, setNotifications] = useState<SocialNotification[]>([]);
  const [groupedNotifications, setGroupedNotifications] = useState<NotificationGroup[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // =====================================================
  // CARREGAR NOTIFICAÇÕES
  // =====================================================
  const loadNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getUserNotifications();
      
      if (result.success && result.data) {
        setNotifications(result.data.notifications);
        setGroupedNotifications(result.data.grouped);
        setUnreadCount(result.data.unreadCount);
      } else {
        setError(result.error || 'Erro ao carregar notificações');
      }
    } catch (err) {
      console.error('Erro no hook de notificações:', err);
      setError('Erro interno');
    } finally {
      setLoading(false);
    }
  }, []);

  // =====================================================
  // MARCAR COMO LIDA
  // =====================================================
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const result = await markNotificationAsRead(notificationId);
      
      if (result.success) {
        // Atualização otimista
        setNotifications(prev => 
          prev.map(notif => 
            notif.id === notificationId 
              ? { ...notif, is_read: true }
              : notif
          )
        );
        
        setGroupedNotifications(prev =>
          prev.map(group => {
            // Se o grupo contém a notificação marcada, atualizar
            const hasNotification = notifications.some(n => 
              n.id === notificationId && 
              n.type === group.type && 
              n.post_id === group.post_id
            );
            
            if (hasNotification) {
              return { ...group, is_read: true };
            }
            return group;
          })
        );
        
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (err) {
      console.error('Erro ao marcar como lida:', err);
    }
  }, [notifications]);

  // =====================================================
  // MARCAR TODAS COMO LIDAS
  // =====================================================
  const markAllAsRead = useCallback(async () => {
    try {
      const result = await markAllNotificationsAsRead();
      
      if (result.success) {
        // Atualização otimista
        setNotifications(prev => 
          prev.map(notif => ({ ...notif, is_read: true }))
        );
        
        setGroupedNotifications(prev =>
          prev.map(group => ({ ...group, is_read: true }))
        );
        
        setUnreadCount(0);
      }
    } catch (err) {
      console.error('Erro ao marcar todas como lidas:', err);
    }
  }, []);

  // =====================================================
  // REFRESH MANUAL
  // =====================================================
  const refresh = useCallback(() => {
    loadNotifications();
  }, [loadNotifications]);

  // =====================================================
  // REAL-TIME SUBSCRIPTIONS
  // =====================================================
  useEffect(() => {
    const supabase = createClient();
    let subscription: any = null;

    const setupRealtimeSubscription = async () => {
      try {
        // Obter usuário atual
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        // Subscription para novas notificações
        subscription = supabase
          .channel('social_notifications')
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'social_notifications',
              filter: `user_id=eq.${user.id}`
            },
            (payload) => {
              console.log('Nova notificação recebida:', payload);
              
              // Recarregar notificações para obter dados completos
              loadNotifications();
              
              // Opcional: Mostrar toast notification
              if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('Nova notificação no Voyagr', {
                  body: 'Você tem uma nova interação social',
                  icon: '/favicon.ico'
                });
              }
            }
          )
          .on(
            'postgres_changes',
            {
              event: 'UPDATE',
              schema: 'public',
              table: 'social_notifications',
              filter: `user_id=eq.${user.id}`
            },
            () => {
              // Recarregar quando notificações são marcadas como lidas
              loadNotifications();
            }
          )
          .subscribe();

      } catch (error) {
        console.error('Erro ao configurar subscription:', error);
      }
    };

    setupRealtimeSubscription();

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [loadNotifications]);

  // =====================================================
  // CARREGAR DADOS INICIAIS
  // =====================================================
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // =====================================================
  // SOLICITAR PERMISSÃO PARA NOTIFICAÇÕES
  // =====================================================
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('Permissão de notificação:', permission);
      });
    }
  }, []);

  return {
    notifications,
    groupedNotifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refresh
  };
}

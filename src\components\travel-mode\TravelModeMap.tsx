"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { MapPin, Navigation, Compass, Target, AlertTriangle, Loader2, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import type { TravelModeLocation, RouteData, AlternativeActivity } from '@/types/travel-mode';

interface TravelModeMapProps {
  currentLocation?: TravelModeLocation;
  destination?: TravelModeLocation;
  route?: RouteData;
  activities?: {
    id: string;
    title: string;
    location: TravelModeLocation;
    status: 'completed' | 'current' | 'upcoming';
  }[];
  alternatives?: AlternativeActivity[];
  className?: string;
  onLocationUpdate?: (location: TravelModeLocation) => void;
  onActivitySelect?: (activityId: string) => void;
}

// Hook customizado para gerenciar o mapa Leaflet
function useLeafletMap() {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const routeLayerRef = useRef<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initializeMap = useCallback(async (center: [number, number]) => {
    if (!mapRef.current || isInitialized || mapInstanceRef.current) return;

    try {
      // Limpar qualquer mapa existente no container
      const container = mapRef.current;
      if ((container as any)._leaflet_id) {
        (container as any)._leaflet_id = null;
      }

      // Importar Leaflet dinamicamente
      const L = (await import('leaflet')).default;
      
      // Configurar ícones do Leaflet
      delete (L.Icon.Default.prototype as any)._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
        iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
      });

      // Criar mapa
      const map = L.map(container).setView(center, 15);

      // Adicionar camada de tiles OpenStreetMap
      L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
      }).addTo(map);

      mapInstanceRef.current = map;
      setIsInitialized(true);
      setError(null);

    } catch (err) {
      console.error('Erro ao inicializar mapa:', err);
      setError('Erro ao carregar o mapa');
    }
  }, [isInitialized]);

  const cleanupMap = useCallback(() => {
    if (mapInstanceRef.current) {
      try {
        mapInstanceRef.current.remove();
      } catch (e) {
        // Ignorar erros de limpeza
      }
      mapInstanceRef.current = null;
      setIsInitialized(false);
    }
  }, []);

  return {
    mapRef,
    mapInstanceRef,
    markersRef,
    routeLayerRef,
    isInitialized,
    error,
    setError,
    initializeMap,
    cleanupMap
  };
}

// Componente do Mapa usando Leaflet
function LeafletMapComponent({
  currentLocation,
  destination,
  route,
  activities = [],
  alternatives = [],
  onLocationUpdate,
  onActivitySelect
}: TravelModeMapProps) {
  const {
    mapRef,
    mapInstanceRef,
    markersRef,
    routeLayerRef,
    isInitialized,
    error,
    setError,
    initializeMap,
    cleanupMap
  } = useLeafletMap();
  
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  // Inicializar mapa Leaflet
  useEffect(() => {
    // Centro inicial do mapa
    const initialCenter = [
      currentLocation?.lat || destination?.lat || -23.5505,
      currentLocation?.lng || destination?.lng || -46.6333
    ] as [number, number];

    // Atraso pequeno para garantir que o DOM esteja pronto
    const timer = setTimeout(() => {
      initializeMap(initialCenter);
    }, 100);

    return () => {
      clearTimeout(timer);
      cleanupMap();
    };
  }, []); // Dependências vazias para executar apenas uma vez

  // Atualizar marcadores quando atividades mudarem
  useEffect(() => {
    if (!mapInstanceRef.current || !isInitialized) return;

    const L = require('leaflet');

    // Limpar marcadores existentes
    markersRef.current.forEach(marker => {
      mapInstanceRef.current.removeLayer(marker);
    });
    markersRef.current = [];

    // Cores dos marcadores por status
    const getMarkerColor = (status: string) => {
      const colors = {
        completed: '#10b981', // verde
        current: '#3b82f6',   // azul
        upcoming: '#6b7280'   // cinza
      };
      return colors[status as keyof typeof colors] || '#6b7280';
    };

    // Criar ícone customizado
    const createCustomIcon = (color: string) => {
      return L.divIcon({
        className: 'custom-map-marker',
        html: `<div style="
          background-color: ${color};
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        "></div>`,
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });
    };

    // Adicionar marcadores das atividades
    activities.forEach(activity => {
      const marker = L.marker([activity.location.lat, activity.location.lng], {
        icon: createCustomIcon(getMarkerColor(activity.status))
      }).addTo(mapInstanceRef.current);

      marker.bindPopup(`
        <div>
          <strong>${activity.title}</strong><br/>
          <small>Status: ${activity.status === 'completed' ? 'Concluído' : 
                          activity.status === 'current' ? 'Atual' : 'Próximo'}</small>
        </div>
      `);

      marker.on('click', () => {
        onActivitySelect?.(activity.id);
      });

      markersRef.current.push(marker);
    });

    // Adicionar marcador da localização atual
    if (currentLocation) {
      const currentMarker = L.marker([currentLocation.lat, currentLocation.lng], {
        icon: createCustomIcon('#ef4444') // vermelho
      }).addTo(mapInstanceRef.current);

      currentMarker.bindPopup(`
        <div>
          <strong>📍 Localização Atual</strong><br/>
          <small>${currentLocation.address || 'Posição atual'}</small>
        </div>
      `);

      markersRef.current.push(currentMarker);
    }

  }, [activities, currentLocation, isInitialized, onActivitySelect]);

  // Adicionar rota quando disponível
  useEffect(() => {
    if (!mapInstanceRef.current || !isInitialized || !route || !currentLocation || !destination) return;

    const L = require('leaflet');

    // Remover rota anterior
    if (routeLayerRef.current) {
      mapInstanceRef.current.removeLayer(routeLayerRef.current);
    }

    // Criar linha da rota simples
    const routeCoords = [
      [currentLocation.lat, currentLocation.lng],
      [destination.lat, destination.lng]
    ] as [number, number][];

    routeLayerRef.current = L.polyline(routeCoords, {
      color: '#3b82f6',
      weight: 4,
      opacity: 0.8
    }).addTo(mapInstanceRef.current);

  }, [route, currentLocation, destination, isInitialized]);

  // Obter localização atual
  const getCurrentLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setError('Geolocalização não suportada');
      return;
    }

    setIsGettingLocation(true);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const newLocation: TravelModeLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          address: 'Localização atual',
          timestamp: new Date().toISOString()
        };
        
        onLocationUpdate?.(newLocation);

        if (mapInstanceRef.current) {
          mapInstanceRef.current.setView([newLocation.lat, newLocation.lng], 16);
        }

        setIsGettingLocation(false);
      },
      (error) => {
        console.error('Erro ao obter localização:', error);
        setError('Erro ao obter localização');
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  }, [onLocationUpdate]);

  // Controles de zoom
  const zoomIn = useCallback(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.zoomIn();
    }
  }, []);

  const zoomOut = useCallback(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.zoomOut();
    }
  }, []);

  if (error) {
    return (
      <div className="relative bg-zinc-100 dark:bg-zinc-900 rounded-lg overflow-hidden flex flex-col items-center justify-center h-full p-6">
        <AlertTriangle className="w-12 h-12 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold text-zinc-900 dark:text-white mb-2">
          Erro no Mapa
        </h3>
        <p className="text-zinc-600 dark:text-zinc-400 text-center mb-4">
          {error}
        </p>
        <Button 
          onClick={() => window.location.reload()} 
          variant="outline" 
          size="sm"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Recarregar
        </Button>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full rounded-lg overflow-hidden">
      {/* Mapa Container */}
      <div
        ref={mapRef}
        className="w-full h-full relative bg-zinc-100"
      />

      {/* Loading overlay */}
      {!isInitialized && (
        <div className="absolute inset-0 bg-zinc-100 dark:bg-zinc-900 flex items-center justify-center rounded-lg">
          <div className="flex items-center gap-3 text-zinc-500 dark:text-zinc-400">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Carregando mapa...</span>
          </div>
        </div>
      )}

      {/* Map Controls */}
      <div className="absolute top-4 left-4 flex flex-col gap-2 z-[1000]">
        <Button
          onClick={getCurrentLocation}
          size="sm"
          variant="secondary"
          className="bg-white dark:bg-zinc-800 shadow-lg"
          disabled={isGettingLocation}
        >
          {isGettingLocation ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Target className="w-4 h-4" />
          )}
        </Button>

        <div className="flex flex-col gap-1">
          <Button
            onClick={zoomIn}
            size="sm"
            variant="secondary"
            className="bg-white dark:bg-zinc-800 shadow-lg w-8 h-8 p-0"
          >
            +
          </Button>
          <Button
            onClick={zoomOut}
            size="sm"
            variant="secondary"
            className="bg-white dark:bg-zinc-800 shadow-lg w-8 h-8 p-0"
          >
            −
          </Button>
        </div>
      </div>

      {/* Route Info */}
      {route && (
        <Card className="absolute top-4 right-4 p-3 bg-white dark:bg-zinc-800 shadow-lg z-[1000]">
          <div className="flex items-center gap-2 text-sm text-zinc-700 dark:text-zinc-300">
            <Navigation className="w-4 h-4" />
            <span>{(route.distance / 1000).toFixed(1)} km</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400">
            <Compass className="w-4 h-4" />
            <span>{Math.round(route.duration / 60)} min</span>
          </div>
        </Card>
      )}

      {/* Legend */}
      <Card className="absolute bottom-4 left-4 p-3 bg-white dark:bg-zinc-800 shadow-lg z-[1000]">
        <h4 className="text-sm font-semibold text-zinc-900 dark:text-white mb-2">
          Legenda
        </h4>
        <div className="space-y-2 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
            <span className="text-zinc-600 dark:text-zinc-400">Concluído</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span className="text-zinc-600 dark:text-zinc-400">Atual</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
            <span className="text-zinc-600 dark:text-zinc-400">Próximo</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span className="text-zinc-600 dark:text-zinc-400">Localização</span>
          </div>
        </div>
      </Card>

      {/* Alternative Activities */}
      {alternatives.length > 0 && (
        <Card className="absolute bottom-4 right-4 p-4 bg-white dark:bg-zinc-800 shadow-lg max-w-xs z-[1000]">
          <h4 className="text-sm font-semibold text-zinc-900 dark:text-white mb-2 flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            Alternativas Próximas
          </h4>
          <div className="space-y-2">
            {alternatives.slice(0, 3).map((alt, index) => (
              <div key={index} className="text-xs">
                <div className="font-medium text-zinc-900 dark:text-white">
                  {alt.title}
                </div>
                <div className="text-zinc-600 dark:text-zinc-400">
                  {alt.location_name}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
}

// Componente principal com carregamento dinâmico
export default function TravelModeMap(props: TravelModeMapProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className={cn(
        "relative bg-zinc-100 dark:bg-zinc-900 rounded-lg overflow-hidden",
        "flex items-center justify-center h-full",
        props.className
      )}>
        <div className="flex items-center gap-3 text-zinc-500 dark:text-zinc-400">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Preparando mapa...</span>
        </div>
      </div>
    );
  }

  return <LeafletMapComponent {...props} />;
}
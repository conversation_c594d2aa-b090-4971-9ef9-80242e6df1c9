"use client";

import React, { useState, useEffect } from 'react';
import { Map, Calendar, Maximize2, Minimize2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import TravelModeTimeline from './TravelModeTimeline';
import TravelModeMap from './TravelModeMap';

interface TimelineActivity {
  id: string;
  title: string;
  description?: string;
  location_name?: string;
  start_time?: string;
  end_time?: string;
  day_number: number;
  status?: 'pending' | 'in_progress' | 'completed' | 'skipped';
  started_at?: string;
  completed_at?: string;
  notes?: string;
}

interface MapActivity {
  id: string;
  title: string;
  location: {
    lat: number;
    lng: number;
    address: string;
  };
  status: 'completed' | 'current' | 'upcoming';
}

interface AlternativeActivity {
  title: string;
  location_name: string;
  estimated_time: number;
  distance_from_route: number;
}

interface TravelModeTimelineMapProps {
  activities: TimelineActivity[];
  currentDay: number;
  currentLocation?: {
    lat: number;
    lng: number;
    address?: string;
  };
  mapActivities?: MapActivity[];
  alternatives?: AlternativeActivity[];
  onActivityAction?: (activityId: string, action: 'start' | 'complete' | 'skip') => void;
  className?: string;
}

export default function TravelModeTimelineMap({
  activities,
  currentDay,
  currentLocation,
  mapActivities = [],
  alternatives = [],
  onActivityAction,
  className
}: TravelModeTimelineMapProps) {
  const [selectedActivityId, setSelectedActivityId] = useState<string | null>(null);
  const [isTimelineExpanded, setIsTimelineExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detectar se é mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Converter atividades da timeline para o formato do mapa
  const convertedMapActivities: MapActivity[] = activities
    .filter(activity => activity.day_number === currentDay)
    .map((activity, index) => ({
      id: activity.id,
      title: activity.title,
      location: {
        lat: -23.5505 - (index * 0.001), // Mock coordinates - replace with real data
        lng: -46.6333 + (index * 0.001),
        address: activity.location_name || 'Localização não especificada'
      },
      status: activity.status === 'completed' ? 'completed' :
              activity.status === 'in_progress' ? 'current' : 'upcoming'
    }));

  const handleActivitySelect = (activityId: string) => {
    setSelectedActivityId(activityId);
    // Scroll to activity on map or highlight it
  };

  const handleMapActivitySelect = (activityId: string) => {
    setSelectedActivityId(activityId);
    // Could scroll timeline to this activity
  };

  if (isMobile) {
    // Layout mobile: Timeline e Mapa em tabs
    return (
      <div className={cn("w-full", className)}>
        <div className="flex items-center justify-center mb-4">
          <div className="flex bg-zinc-100 dark:bg-zinc-800 rounded-lg p-1">
            <button
              onClick={() => setIsTimelineExpanded(false)}
              className={cn(
                "flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                !isTimelineExpanded 
                  ? "bg-white dark:bg-zinc-700 text-zinc-900 dark:text-white shadow-sm"
                  : "text-zinc-600 dark:text-zinc-300 hover:text-zinc-900 dark:hover:text-white"
              )}
            >
              <Map className="w-4 h-4" />
              Mapa
            </button>
            <button
              onClick={() => setIsTimelineExpanded(true)}
              className={cn(
                "flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                isTimelineExpanded 
                  ? "bg-white dark:bg-zinc-700 text-zinc-900 dark:text-white shadow-sm"
                  : "text-zinc-600 dark:text-zinc-300 hover:text-zinc-900 dark:hover:text-white"
              )}
            >
              <Calendar className="w-4 h-4" />
              Timeline
            </button>
          </div>
        </div>

        <div className="h-[600px]">
          {isTimelineExpanded ? (
            <div className="h-full overflow-y-auto bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-4">
              <TravelModeTimeline
                activities={activities}
                currentDay={currentDay}
                onActivitySelect={handleActivitySelect}
                onActivityAction={onActivityAction}
              />
            </div>
          ) : (
            <div className="h-full rounded-lg overflow-hidden border border-zinc-200 dark:border-zinc-800">
              <TravelModeMap
                currentLocation={currentLocation}
                activities={convertedMapActivities}
                alternatives={alternatives}
                onActivitySelect={handleMapActivitySelect}
              />
            </div>
          )}
        </div>
      </div>
    );
  }

  // Calcular altura baseada no número de atividades
  const dayActivities = activities.filter(activity => activity.day === currentDay);
  const timelineHeight = Math.max(600, dayActivities.length * 120 + 200); // Altura mínima maior para melhor visualização

  // Layout desktop: Timeline e Mapa lado a lado
  return (
    <div className={cn("w-full", className)}>
      <div className="flex gap-6 h-full">
        {/* Timeline - Lado Esquerdo */}
        <div className="w-1/3 min-w-[350px] flex flex-col">
          <div
            className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 overflow-hidden flex-1"
            style={{ height: `${timelineHeight}px` }}
          >
            <div className="h-full overflow-y-auto p-4">
              <TravelModeTimeline
                activities={activities}
                currentDay={currentDay}
                onActivitySelect={handleActivitySelect}
                onActivityAction={onActivityAction}
              />
            </div>
          </div>
        </div>

        {/* Mapa - Lado Direito */}
        <div className="flex-1">
          <div
            className="rounded-lg overflow-hidden border border-zinc-200 dark:border-zinc-800"
            style={{ height: `${timelineHeight}px` }}
          >
            <TravelModeMap
              currentLocation={currentLocation}
              activities={convertedMapActivities}
              alternatives={alternatives}
              onActivitySelect={handleMapActivitySelect}
              className="w-full h-full"
            />
          </div>
        </div>
      </div>

      {/* Activity Selection Indicator */}
      {selectedActivityId && (
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center gap-2 text-sm text-blue-800 dark:text-blue-200">
            <Map className="w-4 h-4" />
            <span>
              Atividade selecionada: {activities.find(a => a.id === selectedActivityId)?.title}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

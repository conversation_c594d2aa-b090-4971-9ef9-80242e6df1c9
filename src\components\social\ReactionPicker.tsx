'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Heart, Laugh, Frown, Angry, Surprise, ThumbsUp } from 'lucide-react';
import type { ReactionType } from '@/types/social';

// =====================================================
// REACTION PICKER - COMPONENTE MODERNO E EFICIENTE
// =====================================================

interface ReactionPickerProps {
  currentReaction: ReactionType | undefined;
  onReactionSelect: (reaction: ReactionType) => void;
  onRemoveReaction: () => void;
  disabled?: boolean;
  className?: string;
}

const REACTIONS: Array<{
  type: ReactionType;
  emoji: string;
  icon: React.ReactNode;
  label: string;
  color: string;
}> = [
  {
    type: 'like',
    emoji: '👍',
    icon: <ThumbsUp className="w-5 h-5" />,
    label: 'Curtir',
    color: 'text-blue-500'
  },
  {
    type: 'love',
    emoji: '❤️',
    icon: <Heart className="w-5 h-5" />,
    label: 'Amar',
    color: 'text-red-500'
  },
  {
    type: 'laugh',
    emoji: '😂',
    icon: <Laugh className="w-5 h-5" />,
    label: 'Rir',
    color: 'text-yellow-500'
  },
  {
    type: 'wow',
    emoji: '😮',
    icon: <Surprise className="w-5 h-5" />,
    label: 'Uau',
    color: 'text-orange-500'
  },
  {
    type: 'sad',
    emoji: '😢',
    icon: <Frown className="w-5 h-5" />,
    label: 'Triste',
    color: 'text-blue-400'
  },
  {
    type: 'angry',
    emoji: '😡',
    icon: <Angry className="w-5 h-5" />,
    label: 'Raiva',
    color: 'text-red-600'
  }
];

export function ReactionPicker({
  currentReaction,
  onReactionSelect,
  onRemoveReaction,
  disabled = false,
  className = ''
}: ReactionPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredReaction, setHoveredReaction] = useState<ReactionType | null>(null);
  const pickerRef = useRef<HTMLDivElement>(null);

  // Fechar picker quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  const handleReactionClick = (reaction: ReactionType) => {
    if (currentReaction === reaction) {
      onRemoveReaction();
    } else {
      onReactionSelect(reaction);
    }
    setIsOpen(false);
  };

  const getCurrentReactionData = () => {
    return REACTIONS.find(r => r.type === currentReaction);
  };

  const currentReactionData = getCurrentReactionData();

  return (
    <div ref={pickerRef} className={`relative ${className}`}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={`flex items-center space-x-2 px-3 py-2 rounded-full transition-all duration-200 ${
          currentReaction
            ? `${currentReactionData?.color} bg-gray-50 hover:bg-gray-100`
            : 'text-gray-600 hover:text-red-600 hover:bg-red-50'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
      >
        {currentReactionData ? (
          <>
            <span className="text-lg">{currentReactionData.emoji}</span>
            <span className="text-sm font-medium hidden sm:block">
              {currentReactionData.label}
            </span>
          </>
        ) : (
          <>
            <Heart className="w-5 h-5" />
            <span className="text-sm font-medium hidden sm:block">Curtir</span>
          </>
        )}
      </button>

      {/* Reaction Picker Popup */}
      {isOpen && (
        <div className="absolute bottom-full left-0 mb-2 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 p-2 flex space-x-1 z-50">
          {REACTIONS.map((reaction) => (
            <button
              key={reaction.type}
              onClick={() => handleReactionClick(reaction.type)}
              onMouseEnter={() => setHoveredReaction(reaction.type)}
              onMouseLeave={() => setHoveredReaction(null)}
              className={`relative p-2 rounded-full transition-all duration-200 hover:scale-125 hover:bg-gray-100 dark:hover:bg-gray-700 ${
                currentReaction === reaction.type ? 'bg-gray-100 dark:bg-gray-700 scale-110' : ''
              }`}
              title={reaction.label}
            >
              <span className="text-xl">{reaction.emoji}</span>
              
              {/* Tooltip */}
              {hoveredReaction === reaction.type && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap">
                  {reaction.label}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900" />
                </div>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

// =====================================================
// REACTION SUMMARY - MOSTRA CONTADORES DE REAÇÕES
// =====================================================

interface ReactionSummaryProps {
  reactions: { [key in ReactionType]?: { count: number; users: any[] } };
  totalCount: number;
  className?: string;
}

export function ReactionSummary({ reactions, totalCount, className = '' }: ReactionSummaryProps) {
  if (totalCount === 0) return null;

  // Ordenar reações por quantidade
  const sortedReactions = Object.entries(reactions)
    .filter(([_, data]) => data && data.count > 0)
    .sort(([, a], [, b]) => (b?.count || 0) - (a?.count || 0))
    .slice(0, 3); // Mostrar apenas as 3 principais

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {/* Emojis das reações principais */}
      <div className="flex -space-x-1">
        {sortedReactions.map(([type, data]) => {
          const reactionData = REACTIONS.find(r => r.type === type);
          if (!reactionData || !data) return null;
          
          return (
            <span
              key={type}
              className="inline-block w-6 h-6 bg-white rounded-full border border-gray-200 flex items-center justify-center text-sm"
              title={`${data.count} ${reactionData.label.toLowerCase()}`}
            >
              {reactionData.emoji}
            </span>
          );
        })}
      </div>

      {/* Contador total */}
      <span className="text-sm text-gray-600 ml-2">
        {totalCount}
      </span>
    </div>
  );
}

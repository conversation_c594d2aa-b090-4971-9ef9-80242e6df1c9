'use server';

// ========================================
// SISTEMA DE MODO VIAGEM - SERVER ACTIONS
// Funcionalidades revolucionárias para viajantes ativos
// ========================================

import { createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import type { 
  TravelModeDashboard,
  TravelModeActivationResponse,
  TravelModeStatusResponse,
  TravelModeLog,
  TravelModeLogType,
  TravelModeNotification,
  DiaryEntry,
  DiaryEntryCreate,
  ExpenseSplit,
  ExpenseSplitRequest,
  TravelModeSettings,
  WeatherData,
  AlternativeActivity,
  TravelModeLocation
} from '@/types/travel-mode';

// ========================================
// TIPOS PARA RESULTADOS DAS ACTIONS
// ========================================

type ActionResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
};

// ========================================
// HELPER FUNCTION
// ========================================

async function safeServerAction<T>(
  operation: () => Promise<T>,
  operationName: string
): Promise<ActionResult<T>> {
  try {
    console.log(`🚀 Starting ${operationName}...`);
    const data = await operation();
    console.log(`✅ ${operationName} completed successfully`);
    return { success: true, data };
  } catch (error) {
    console.error(`❌ Error in ${operationName}:`, error);
    console.error(`❌ Error stack:`, error instanceof Error ? error.stack : 'No stack trace');
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

// ========================================
// ATIVAÇÃO E STATUS DO MODO VIAGEM
// ========================================

export async function activateTravelModeAction(
  itineraryId: string
): Promise<ActionResult<TravelModeActivationResponse>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      throw new Error('Usuário não autenticado');
    }

    const { data, error } = await supabase.rpc('activate_travel_mode', {
      p_itinerary_id: itineraryId,
      p_user_id: user.id
    });

    if (error) {
      throw new Error(error.message);
    }

    if (!data?.success) {
      throw new Error(data?.error || 'Falha ao ativar modo viagem');
    }

    revalidatePath(`/itineraries/${itineraryId}`);
    revalidatePath(`/travel-mode/${itineraryId}`);

    return data;
  }, 'activateTravelMode');
}

export async function checkTravelModeStatusAction(
  itineraryId: string
): Promise<ActionResult<TravelModeStatusResponse>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    // Verificar se o modo viagem pode ser ativado baseado nas datas
    const { data: canActivate } = await supabase.rpc('check_travel_mode_status', {
      p_itinerary_id: itineraryId
    });

    // Verificar se está atualmente ativo
    const { data: liveData } = await supabase
      .from('travel_mode_live_data')
      .select('*')
      .eq('itinerary_id', itineraryId)
      .single();

    // Buscar dados do roteiro
    const { data: itinerary, error: itineraryError } = await supabase
      .from('travel_itineraries')
      .select('title, start_date, end_date, user_id')
      .eq('id', itineraryId)
      .single();

    console.log('Itinerary query result:', { itinerary, itineraryError, itineraryId });

    if (!itinerary) {
      throw new Error('Roteiro não encontrado');
    }

    const today = new Date();
    const startDate = itinerary.start_date ? new Date(itinerary.start_date) : null;
    const endDate = itinerary.end_date ? new Date(itinerary.end_date) : null;

    let days_until_start: number | null = null;
    let days_since_start: number | null = null;
    let reason: string | null = null;

    if (startDate) {
      const timeDiff = startDate.getTime() - today.getTime();
      const daysUntil = Math.ceil(timeDiff / (1000 * 3600 * 24));
      
      if (daysUntil > 1) {
        days_until_start = daysUntil;
        reason = `Viagem começa em ${daysUntil} dias`;
      } else if (daysUntil === 1) {
        days_until_start = 1;
        reason = 'Viagem começa amanhã!';
      } else if (daysUntil === 0) {
        days_until_start = 0;
        reason = 'Viagem começa hoje!';
      } else {
        const timeSinceStart = today.getTime() - startDate.getTime();
        days_since_start = Math.floor(timeSinceStart / (1000 * 3600 * 24));
        
        if (endDate && today > endDate) {
          reason = 'Viagem finalizada';
        } else {
          reason = 'Viagem em andamento';
        }
      }
    } else {
      reason = 'Datas da viagem não definidas';
    }

    return {
      is_active: !!liveData,
      can_activate: Boolean(canActivate),
      days_until_start,
      days_since_start,
      reason,
      itinerary_title: itinerary?.title,
      start_date: itinerary?.start_date,
      end_date: itinerary?.end_date,
      participant_count: 1 // Default value for now
    };
  }, 'checkTravelModeStatus');
}

export async function deactivateTravelModeAction(
  itineraryId: string
): Promise<ActionResult<{ success: boolean }>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    // Remover dados ao vivo
    const { error } = await supabase
      .from('travel_mode_live_data')
      .delete()
      .eq('itinerary_id', itineraryId);

    if (error) {
      throw new Error(error.message);
    }

    // Log de desativação
    await supabase
      .from('travel_mode_logs')
      .insert({
        itinerary_id: itineraryId,
        log_type: 'mode_activated',
        content: { action: 'deactivated' }
      });

    revalidatePath(`/itineraries/${itineraryId}`);
    revalidatePath(`/travel-mode/${itineraryId}`);

    return { success: true };
  }, 'deactivateTravelMode');
}

// ========================================
// DASHBOARD E DADOS EM TEMPO REAL
// ========================================

export async function getTravelModeDashboardAction(
  itineraryId: string
): Promise<ActionResult<TravelModeDashboard>> {
  return safeServerAction(async () => {
    console.log('🚀 getTravelModeDashboardAction called with itineraryId:', itineraryId);

    const supabase = await createClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('❌ User authentication error:', userError);
      throw new Error('Usuário não autenticado');
    }

    console.log('✅ User authenticated:', user.id);

    const { data, error } = await supabase.rpc('get_travel_mode_dashboard_real', {
      p_itinerary_id: itineraryId,
      p_user_id: user.id
    });

    if (error) {
      console.error('❌ Dashboard RPC error:', error);
      throw new Error(`Erro na consulta: ${error.message}`);
    }

    console.log('📊 Dashboard RPC response:', data);

    if (!data?.success) {
      console.error('❌ Dashboard data error:', data);
      throw new Error(data?.error || 'Modo viagem não ativado');
    }

    console.log('✅ Dashboard loaded successfully');
    return data;
  }, 'getTravelModeDashboard');
}

export async function updateLocationAction(
  itineraryId: string,
  location: TravelModeLocation
): Promise<ActionResult<{ success: boolean }>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    // Atualizar localização nos dados ao vivo
    const { error } = await supabase
      .from('travel_mode_live_data')
      .update({
        current_location: location,
        updated_at: new Date().toISOString()
      })
      .eq('itinerary_id', itineraryId);

    if (error) {
      throw new Error(error.message);
    }

    // Log da atualização de localização
    await supabase
      .from('travel_mode_logs')
      .insert({
        itinerary_id: itineraryId,
        log_type: 'location_update',
        content: { location },
        location_lat: location.lat,
        location_lng: location.lng
      });

    revalidatePath(`/travel-mode/${itineraryId}`);

    return { success: true };
  }, 'updateLocation');
}

// ========================================
// LOGS E NOTIFICAÇÕES
// ========================================

export async function addTravelModeLogAction(
  itineraryId: string,
  logType: TravelModeLogType,
  content: Record<string, any>,
  activityId?: string,
  location?: TravelModeLocation
): Promise<ActionResult<TravelModeLog>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    const logData = {
      itinerary_id: itineraryId,
      log_type: logType,
      content,
      activity_id: activityId,
      location_lat: location?.lat,
      location_lng: location?.lng,
      metadata: {
        timestamp: new Date().toISOString(),
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : null
      }
    };

    const { data, error } = await supabase
      .from('travel_mode_logs')
      .insert(logData)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    revalidatePath(`/travel-mode/${itineraryId}`);

    return data;
  }, 'addTravelModeLog');
}

export async function markNotificationReadAction(
  notificationId: string
): Promise<ActionResult<{ success: boolean }>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    const { error } = await supabase
      .from('travel_mode_notifications')
      .update({ is_read: true })
      .eq('id', notificationId);

    if (error) {
      throw new Error(error.message);
    }

    revalidatePath('/travel-mode');

    return { success: true };
  }, 'markNotificationRead');
}

export async function createNotificationAction(
  itineraryId: string,
  notification: Omit<TravelModeNotification, 'id' | 'itinerary_id' | 'user_id' | 'is_read' | 'created_at'>
): Promise<ActionResult<TravelModeNotification>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('travel_mode_notifications')
      .insert({
        itinerary_id: itineraryId,
        ...notification
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    revalidatePath(`/travel-mode/${itineraryId}`);

    return data;
  }, 'createNotification');
}

// ========================================
// DIÁRIO DE VIAGEM
// ========================================

export async function createDiaryEntryAction(
  itineraryId: string,
  entry: DiaryEntryCreate
): Promise<ActionResult<DiaryEntry>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('travel_mode_diary_entries')
      .insert({
        itinerary_id: itineraryId,
        ...entry
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Log da criação do diário
    await addTravelModeLogAction(
      itineraryId,
      'photo_upload',
      {
        diary_entry_id: data.id,
        title: entry.title,
        photos_count: entry.photos?.length || 0
      }
    );

    revalidatePath(`/travel-mode/${itineraryId}`);

    return data;
  }, 'createDiaryEntry');
}

export async function getDiaryEntriesAction(
  itineraryId: string,
  dayNumber?: number
): Promise<ActionResult<DiaryEntry[]>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    let query = supabase
      .from('travel_mode_diary_entries')
      .select('*')
      .eq('itinerary_id', itineraryId)
      .order('created_at', { ascending: false });

    if (dayNumber !== undefined) {
      query = query.eq('day_number', dayNumber);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  }, 'getDiaryEntries');
}

export async function updateDiaryEntryAction(
  entryId: string,
  updates: Partial<DiaryEntryCreate>
): Promise<ActionResult<DiaryEntry>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('travel_mode_diary_entries')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', entryId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    revalidatePath('/travel-mode');

    return data;
  }, 'updateDiaryEntry');
}

// ========================================
// DIVISÃO DE DESPESAS
// ========================================

export async function createExpenseSplitAction(
  splitRequest: ExpenseSplitRequest
): Promise<ActionResult<ExpenseSplit[]>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    // Buscar dados da despesa
    const { data: expense, error: expenseError } = await supabase
      .from('itinerary_expenses')
      .select('itinerary_id, currency, actual_amount')
      .eq('id', splitRequest.expense_id)
      .single();

    if (expenseError || !expense) {
      throw new Error('Despesa não encontrada');
    }

    // Criar as divisões
    const splitsData = splitRequest.splits.map(split => ({
      expense_id: splitRequest.expense_id,
      itinerary_id: expense.itinerary_id,
      participant_id: split.participant_id,
      amount: split.amount,
      currency: expense.currency
    }));

    const { data, error } = await supabase
      .from('travel_mode_expense_splits')
      .insert(splitsData)
      .select();

    if (error) {
      throw new Error(error.message);
    }

    // Log da divisão de despesa
    await addTravelModeLogAction(
      expense.itinerary_id,
      'expense_added',
      {
        expense_id: splitRequest.expense_id,
        splits_count: splitRequest.splits.length,
        total_amount: splitRequest.splits.reduce((sum, split) => sum + split.amount, 0)
      }
    );

    revalidatePath(`/travel-mode/${expense.itinerary_id}`);

    return data || [];
  }, 'createExpenseSplit');
}

export async function markExpensePaidAction(
  splitId: string,
  paymentMethod?: string
): Promise<ActionResult<{ success: boolean }>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    const { error } = await supabase
      .from('travel_mode_expense_splits')
      .update({
        is_paid: true,
        payment_method: paymentMethod,
        updated_at: new Date().toISOString()
      })
      .eq('id', splitId);

    if (error) {
      throw new Error(error.message);
    }

    revalidatePath('/travel-mode');

    return { success: true };
  }, 'markExpensePaid');
}

// ========================================
// CONFIGURAÇÕES
// ========================================

export async function getTravelModeSettingsAction(
  itineraryId: string
): Promise<ActionResult<TravelModeSettings | null>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('travel_mode_settings')
      .select('*')
      .eq('itinerary_id', itineraryId)
      .single();

    if (error && error.code !== 'PGRST116') { // Not found é ok
      throw new Error(error.message);
    }

    return data;
  }, 'getTravelModeSettings');
}

export async function updateTravelModeSettingsAction(
  itineraryId: string,
  settings: Partial<TravelModeSettings>
): Promise<ActionResult<TravelModeSettings>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('travel_mode_settings')
      .upsert({
        itinerary_id: itineraryId,
        ...settings,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    revalidatePath(`/travel-mode/${itineraryId}`);

    return data;
  }, 'updateTravelModeSettings');
}

// ========================================
// SUGESTÕES DE ATIVIDADES ALTERNATIVAS
// ========================================

export async function generateAlternativeActivitiesAction(
  itineraryId: string,
  currentActivityId: string,
  reason: 'weather' | 'time' | 'preference' | 'budget'
): Promise<ActionResult<AlternativeActivity[]>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    // Buscar atividade atual e dados do roteiro
    const { data: currentActivity } = await supabase
      .from('itinerary_activities')
      .select(`
        *,
        itinerary_days!inner(
          itinerary_id,
          day_number,
          travel_itineraries!inner(
            title,
            cities,
            countries
          )
        )
      `)
      .eq('id', currentActivityId)
      .single();

    if (!currentActivity) {
      throw new Error('Atividade não encontrada');
    }

    // Gerar sugestões baseadas no motivo
    const alternatives: AlternativeActivity[] = [];

    if (reason === 'weather') {
      // Atividades indoor como alternativa
      alternatives.push({
        id: 'alt-indoor-1',
        title: 'Museu Local',
        description: 'Explore a cultura local em ambiente coberto',
        location_name: 'Centro da cidade',
        activity_type: 'cultural',
        reason: 'Alternativa indoor devido ao clima',
        weather_dependent: false,
        cost_estimate: 25,
        rating: 4.5
      });
    }

    // Salvar alternativas nos dados ao vivo
    await supabase
      .from('travel_mode_live_data')
      .update({
        alternative_activities: alternatives,
        updated_at: new Date().toISOString()
      })
      .eq('itinerary_id', itineraryId);

    // Log da geração de sugestões
    await addTravelModeLogAction(
      itineraryId,
      'suggestion_generated',
      {
        reason,
        current_activity_id: currentActivityId,
        alternatives_count: alternatives.length
      }
    );

    revalidatePath(`/travel-mode/${itineraryId}`);

    return alternatives;
  }, 'generateAlternativeActivities');
}

// ========================================
// INTEGRAÇÃO COM CLIMA
// ========================================

export async function updateWeatherDataAction(
  itineraryId: string,
  weatherData: WeatherData
): Promise<ActionResult<{ success: boolean }>> {
  return safeServerAction(async () => {
    const supabase = await createClient();

    // Atualizar dados do clima
    const { error } = await supabase
      .from('travel_mode_live_data')
      .update({
        weather_data: weatherData,
        updated_at: new Date().toISOString()
      })
      .eq('itinerary_id', itineraryId);

    if (error) {
      throw new Error(error.message);
    }

    // Criar alertas se necessário
    if (weatherData.alerts && weatherData.alerts.length > 0) {
      for (const alert of weatherData.alerts) {
        await createNotificationAction(itineraryId, {
          notification_type: 'weather_alert',
          title: 'Alerta Meteorológico',
          message: alert.message,
          priority: alert.severity === 'extreme' ? 'urgent' : 
                   alert.severity === 'high' ? 'high' : 'medium',
          data: { alert },
          expires_at: alert.expires_at
        });
      }
    }

    revalidatePath(`/travel-mode/${itineraryId}`);

    return { success: true };
  }, 'updateWeatherData');
}

export async function autoActivateTravelMode(itineraryId: string) {
  try {
    const supabase = await createClient();

    // Chamar função do banco para ativação automática
    const { data, error } = await supabase.rpc('auto_activate_travel_mode', {
      p_itinerary_id: itineraryId
    });

    if (error) {
      console.error('Erro na ativação automática:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Erro na ativação automática do modo viagem:', error);
    throw error;
  }
}

/**
 * Marcar atividade como concluída
 */
export async function markActivityCompletedAction(
  itineraryId: string,
  activityId: string,
  notes?: string,
  location?: { lat: number; lng: number }
): Promise<ActionResult<{ success: boolean; nextActivity?: any }>> {
  return safeServerAction(async () => {
    console.log('🎯 markActivityCompletedAction called with params:', {
      itineraryId,
      activityId,
      notes,
      location,
      timestamp: new Date().toISOString()
    });

    console.log('📡 Creating Supabase client...');
    const supabase = await createClient();
    console.log('✅ Supabase client created');

    // Get current user
    console.log('🔐 Getting current user...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('❌ User authentication error:', userError);
      throw new Error('Usuário não autenticado');
    }

    console.log('✅ User authenticated:', { userId: user.id, email: user.email });

    console.log('🔧 Calling mark_activity_completed RPC with params:', {
      p_itinerary_id: itineraryId,
      p_activity_id: activityId,
      p_user_id: user.id,
      p_notes: notes || null,
      p_location_lat: location?.lat || null,
      p_location_lng: location?.lng || null
    });

    const { data, error } = await supabase.rpc('mark_activity_completed', {
      p_itinerary_id: itineraryId,
      p_activity_id: activityId,
      p_user_id: user.id,
      p_notes: notes || null,
      p_location_lat: location?.lat || null,
      p_location_lng: location?.lng || null
    });

    console.log('📡 RPC call completed. Error:', error, 'Data:', data);

    if (error) {
      console.error('❌ Mark activity completed RPC error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      throw new Error(`Erro ao marcar atividade: ${error.message}`);
    }

    console.log('📊 Mark activity completed response:', data);

    if (!data?.success) {
      console.error('❌ Mark activity completed data error:', data);
      throw new Error(data?.error || 'Erro ao marcar atividade como concluída');
    }

    // Revalidar cache
    revalidatePath(`/travel-mode/${itineraryId}`);

    console.log('✅ Activity marked as completed successfully');
    return data;
  }, 'markActivityCompleted');
}

/**
 * Iniciar atividade
 */
export async function startActivityAction(
  itineraryId: string,
  activityId: string,
  location?: { lat: number; lng: number }
): Promise<ActionResult<{ success: boolean }>> {
  return safeServerAction(async () => {
    console.log('🚀 startActivityAction called:', { itineraryId, activityId });

    const supabase = await createClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('❌ User authentication error:', userError);
      throw new Error('Usuário não autenticado');
    }

    console.log('✅ User authenticated:', user.id);

    const { data, error } = await supabase.rpc('start_activity', {
      p_itinerary_id: itineraryId,
      p_activity_id: activityId,
      p_user_id: user.id,
      p_location_lat: location?.lat || null,
      p_location_lng: location?.lng || null
    });

    if (error) {
      console.error('❌ Start activity RPC error:', error);
      throw new Error(`Erro ao iniciar atividade: ${error.message}`);
    }

    console.log('📊 Start activity response:', data);

    if (!data?.success) {
      console.error('❌ Start activity data error:', data);
      throw new Error(data?.error || 'Erro ao iniciar atividade');
    }

    // Revalidar cache
    revalidatePath(`/travel-mode/${itineraryId}`);

    console.log('✅ Activity started successfully');
    return data;
  }, 'startActivity');
}

/**
 * Pular atividade
 */
export async function skipActivityAction(
  itineraryId: string,
  activityId: string,
  reason?: string
): Promise<ActionResult<{ success: boolean; nextActivity?: any }>> {
  return safeServerAction(async () => {
    console.log('⏭️ skipActivityAction called:', { itineraryId, activityId, reason });

    const supabase = await createClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('❌ User authentication error:', userError);
      throw new Error('Usuário não autenticado');
    }

    console.log('✅ User authenticated:', user.id);

    const { data, error } = await supabase.rpc('skip_activity', {
      p_itinerary_id: itineraryId,
      p_activity_id: activityId,
      p_user_id: user.id,
      p_reason: reason || null
    });

    if (error) {
      console.error('❌ Skip activity RPC error:', error);
      throw new Error(`Erro ao pular atividade: ${error.message}`);
    }

    console.log('📊 Skip activity response:', data);

    if (!data?.success) {
      console.error('❌ Skip activity data error:', data);
      throw new Error(data?.error || 'Erro ao pular atividade');
    }

    // Revalidar cache
    revalidatePath(`/travel-mode/${itineraryId}`);

    console.log('✅ Activity skipped successfully');
    return data;
  }, 'skipActivity');
}

 